# **Troubleshooting File Share Access Denied for Domain Admins on macOS High Sierra**

## **1\. Introduction: Defining the Problem \- Windows Server 2019 File Share Access Issue with macOS High Sierra Clients**

This report addresses a specific and challenging file share access issue encountered in a mixed operating system environment. The problem manifests as a subset of users who are members of the Domain Admins group being unexpectedly denied access to a particular subfolder residing on a Windows Server 2019 file share. This access denial occurs exclusively when these users attempt to access the share via macOS High Sierra clients. Notably, these same users, by virtue of their Domain Admins group membership, possess full control permissions on the parent folder of the inaccessible subfolder. Furthermore, Windows-based clients accessing the same file share and subfolder do not experience this issue.

The primary objective of this report is to provide a comprehensive, step-by-step guide for IT administrators and system engineers to effectively troubleshoot and ultimately resolve this perplexing access denial. This will involve a detailed exploration of the underlying Windows Server file share permission mechanisms, an examination of the nuances of the SMB (Server Message Block) protocol as implemented in both Windows Server 2019 and macOS High Sierra, and a consideration of specific configuration settings on both the server and the client operating systems. By systematically investigating potential causes and applying targeted solutions, this report aims to restore seamless access for the affected Domain Admins while also providing insights into preventing similar issues in the future.

The successful resolution of this issue is of significant importance. Domain Admins typically require unfettered access to file share resources for critical administrative tasks. The inability to access necessary subfolders can impede these tasks, potentially leading to operational delays and security vulnerabilities. Moreover, inconsistencies in access based on the client operating system can create confusion and undermine user productivity. Therefore, a thorough understanding of the root cause and the implementation of a robust solution are paramount for maintaining a secure and efficient IT infrastructure.

## **2\. Understanding Windows Server File Share Permissions and Inheritance**

To effectively diagnose the reported issue, a foundational understanding of how Windows Server manages file share permissions is essential. This involves the interplay between NTFS permissions, share permissions, and the concepts of explicit and inherited permissions.

### **2.1 How NTFS and Share Permissions Interact**

Windows Server employs a dual-layered security model for controlling access to files and folders: NTFS permissions and share permissions 1. **NTFS permissions** are the more granular of the two and are applied to every file and folder residing on a volume formatted with the NTFS file system. These permissions, such as Read, Write, Modify, and Full Control, dictate what actions a user or group can perform on a specific object, regardless of whether the access is attempted locally on the server or remotely over the network 2. NTFS permissions can be further broken down into basic and advanced permissions, offering a detailed level of control over object access 2.

**Share permissions**, on the other hand, are specifically applied to shared folders and come into effect only when a user attempts to access the shared folder over a network from a remote system 1. Share permissions are less granular than NTFS permissions, typically offering three levels of access: Read, Change, and Full Control 2. These permissions act as an initial gatekeeper, determining whether a network connection to the shared resource is even permitted.

When a file or folder within a share is accessed over the network, both NTFS and share permissions are evaluated 2. In such scenarios, the principle of "**the most restrictive wins**" is applied 1. For instance, if a user has 'Full Control' share permissions to a folder but only 'Read' NTFS permissions on a specific subfolder within it, their effective access to that subfolder over the network will be 'Read'. Conversely, 'Read' share permissions combined with 'Full Control' NTFS permissions will also result in 'Read' effective access 2. Therefore, while the initial report mentions full control on the parent folder (likely an NTFS permission), it is important to consider the share permissions configured for the parent share as well, although the macOS-specific nature of the issue suggests that the NTFS permissions on the subfolder are the more probable area of concern.

Permissions can be assigned in two ways: **explicitly** or through **inheritance** 6. Explicit permissions are those directly assigned to a specific object, such as a user or group being granted 'Modify' access to a particular file 6. Inherited permissions, conversely, are propagated automatically from a parent object (like a folder) to its child objects (subfolders and files) 3. Inheritance simplifies the task of managing permissions across a hierarchical file structure and ensures a degree of consistency 3. Windows allows administrators to view which permissions are inherited on an object through its security settings 7.

However, the inheritance of permissions can be **broken** at any level within the folder structure 8. When inheritance is disabled on an object, the inherited permissions are typically converted into explicit permissions on that object, which can then be modified independently of the parent 8. This ability to break inheritance is crucial for creating unique permission sets for specific subfolders or files. In the context of the reported issue, if inheritance was broken on the specific subfolder, the permissions configured there might not align with those of the parent folder, potentially explaining why Domain Admins are being denied access despite having full control on the parent. It is important to note that explicit permissions take precedence over inherited permissions, with explicit "Deny" permissions holding the highest precedence, followed by explicit "Allow," then inherited "Deny," and finally inherited "Allow" 10.

Changes made to the permissions of a parent folder can often be **propagated** to its child objects, ensuring that permissions are consistently applied throughout the hierarchy 1. Windows provides an option during permission modifications to "Replace all child object permissions entries with inheritable permission entries from this object," which forces the child objects to inherit the parent's permissions, effectively overwriting any unique permissions previously set 1.

### **2.2 The Role of Domain Admins and Potential Pitfalls**

The **Domain Admins** group is a built-in security group in Active Directory that holds significant privileges within the domain 12. Members of this group are granted administrative rights across the entire domain infrastructure, including domain controllers, member servers, and workstations. Typically, the Domain Admins group is also a member of the local Administrators group on all domain-joined machines, such as the Windows Server 2019 file server in question 12. This membership grants them broad control over the local operating system, including the ability to manage files, folders, and services.

However, even members of the Domain Admins group can encounter access denied errors under certain circumstances. **User Account Control (UAC)**, a security feature in Windows, can sometimes affect administrative privileges, particularly during local logon sessions via Remote Desktop Protocol (RDP) 12. UAC operates by running standard user processes with a limited set of privileges, even if the user is an administrator. Administrative tasks often require an elevated process, and UAC prompts the user for consent before granting these elevated privileges 12. While the reported issue occurs during network access from macOS clients, understanding UAC's role is important context, especially if local administration of the file server was involved in setting up the permissions. Some best practices, as suggested in 13, involve creating dedicated file share administrator groups to manage permissions, which can sometimes help avoid UAC-related complexities when logged in locally on the server.

A primary reason why even Domain Admins might be denied access is the presence of **unexpected "Deny" permissions** 2. Explicitly setting a "Deny" permission on a resource for a user or group will always take precedence over any "Allow" permissions that might also apply, either explicitly or through group membership 2. This means that if the Domain Admins group, or a group that the affected users are members of, has an explicit "Deny" ACE (Access Control Entry) on the specific subfolder, they will be denied access regardless of the full control permissions on the parent folder 15. It is also possible that less privileged users with sufficient permissions to manage ACLs on the subfolder might have inadvertently or intentionally modified the permissions, leading to the removal or denial of access for administrative groups 15.

Finally, the **ownership** of a file or folder plays a role in permission management 1. The owner of an object has inherent rights to modify its permissions. If the ownership of the specific subfolder has been changed from the default (typically the local Administrators group or Domain Admins) to another user or group, the current owner might have inadvertently misconfigured the permissions, leading to the access denial. However, administrators retain the ability to take ownership of any object, which allows them to then modify its permissions as needed 16.

### **2.3 Common Reasons for Permission Inheritance Issues**

Several scenarios can lead to problems with permission inheritance, resulting in unexpected access issues. **Broken inheritance** occurs when a child object, such as the specific subfolder in this case, no longer inherits permissions from its parent folder 11. This can happen if an administrator explicitly modifies the permissions on the child object, effectively making them unique 11.

Permissions can also be affected by the **accidental removal of permissions** 15. Users with the necessary privileges might inadvertently remove or modify existing permissions, including those assigned to administrative groups like Domain Admins 15.

Issues can also arise during **permission propagation** 17. For example, if a folder is moved within the same volume, the inherited permissions on the moved folder and its contents might not be automatically updated 17. This is because, within the same volume, the file is not actually moved but rather its metadata is updated, and the original security descriptor, including ACLs, is retained 18. Consequently, if the specific subfolder was moved from a location with different permissions, this could contribute to the observed problem.

**Conflicting explicit permissions** set at various levels within the folder hierarchy can also lead to unexpected access outcomes 1. If there are explicit permissions set on the subfolder that conflict with the inherited permissions from the parent, the explicit permissions will take precedence 10.

Another potential cause is the presence of **unknown or orphaned SIDs** in the permission entries 1. These Security Identifiers might refer to user or group accounts that have been deleted from Active Directory. While these SIDs don't directly grant or deny access, their presence can sometimes complicate the interpretation and management of permissions.

Finally, **third-party tools or scripts** used for managing file share permissions might not always handle inheritance correctly, potentially leading to unintended permission configurations 17.

## **3\. macOS High Sierra and SMB Protocol Nuances**

Given that the access denial is specific to macOS High Sierra clients, it is crucial to understand the intricacies of how this operating system interacts with Windows file shares, particularly concerning the SMB protocol.

### **3.1 Differences in SMB Implementation Compared to Windows**

macOS utilizes its own native **SMB client** to communicate with Windows file shares 19. While the SMB protocol aims to provide cross-platform file sharing capabilities, the specific implementation can vary between operating systems, sometimes leading to compatibility issues.

The process of **protocol version negotiation** is critical when a macOS client attempts to connect to a Windows Server. Both the client and the server will attempt to agree on the highest SMB protocol version they both support (such as SMB1, SMB2, or SMB3) 20. macOS High Sierra might default to a specific SMB version that could interact differently with the Windows Server 2019 configuration or the permissions set on the subfolder. Research suggests that macOS High Sierra has experienced issues with SMB, and in some cases, forcing the client to use a specific version, such as SMB2, has resolved connectivity or stability problems 22. Windows Server 2019 generally supports SMB 3.x, and any inconsistencies or bugs in High Sierra's SMB 3 implementation when interacting with specific permission configurations on the server could potentially trigger the access denial.

**SMB signing** is a security feature that adds a digital signature to SMB packets to prevent man-in-the-middle attacks 20. While beneficial for security, SMB signing can sometimes impact performance, and macOS clients, particularly in older versions like High Sierra, might require manual adjustments to signing settings for optimal performance 21. Although the current issue is focused on access denial rather than performance, incorrect signing configurations or a mismatch in signing requirements between the client and server could potentially contribute to connection or authentication problems that manifest as access denial.

There can also be **differences in file locking mechanisms** between macOS and Windows when using SMB 25. An overly aggressive file locking implementation on the macOS client, as has been reported in some versions, could potentially lead to permission-related errors, although this is less likely to be the direct cause of a persistent access denial for a group like Domain Admins.

Finally, macOS and Windows handle **file metadata** differently 19. macOS uses resource forks and .DS\_Store files to store metadata, and the way these are handled over SMB to a Windows file system can sometimes lead to complexities, although these are not typically associated with outright permission denial.

### **3.2 Known Compatibility Issues and Configuration Considerations for macOS High Sierra**

macOS High Sierra is known to have had several **compatibility issues and instabilities** related to its SMB implementation 22. Users have reported various problems, including connection failures, freezes, and crashes when accessing SMB shares on different server platforms. Some of these issues were addressed in later updates to High Sierra 22.

Another key consideration is the file system used by macOS High Sierra. If High Sierra is installed on an SSD, it often automatically converts the system volume to **APFS (Apple File System)** 28. Notably, macOS does not support sharing APFS formatted volumes using **AFP (Apple Filing Protocol)**, which was traditionally the preferred file sharing protocol for macOS clients. This limitation necessitates the use of SMB for connecting to Windows file shares 28.

While generally not a direct cause of permission denial in this context, it's worth noting the differences in **case sensitivity** between macOS (case-insensitive by default, with a case-sensitive option for APFS) and Windows (case-insensitive). This could potentially lead to confusion if file or folder names are inconsistently cased, but it's unlikely to be the primary reason for Domain Admins being denied access when the parent folder access works.

A crucial aspect of troubleshooting SMB issues on macOS is the **/etc/nsmb.conf** file 21. This configuration file allows administrators to fine-tune the behavior of the macOS SMB client. It can be used to specify the preferred SMB protocol version, control SMB signing, disable directory caching, and adjust other parameters that can affect connectivity, performance, and compatibility with different SMB server implementations. Given the history of SMB issues with High Sierra, examining and potentially modifying the nsmb.conf file on the affected macOS clients is a critical troubleshooting step. Forcing the client to use SMB2 by setting protocol\_vers\_map=2 in this file has been suggested as a workaround for various SMB-related problems 22. Similarly, disabling SMB signing (signing\_required=no) has also been recommended in some scenarios, although the security implications should be considered 21. Other settings like file\_ids\_off=yes might also be relevant depending on the specific issues encountered 23. If the /etc/nsmb.conf file does not exist, it can be created using the sudo command in Terminal 23.

**Keychain Access** is the built-in password management system in macOS, and it stores network passwords, including those used to connect to SMB shares 29. If the passwords stored in Keychain Access for the file share server are outdated or incorrect, this could lead to authentication failures that manifest as access denial. Therefore, checking and potentially deleting or updating the saved SMB passwords in Keychain Access on the affected macOS clients is an important troubleshooting step 29.

Finally, the **File Sharing preferences** in macOS System Preferences allow users to enable and configure file sharing, including SMB sharing 28. Verifying these settings on the macOS clients can ensure that SMB sharing is enabled and configured for the appropriate user accounts that are attempting to access the Windows file share.

## **4\. Step-by-Step Troubleshooting Guide**

To effectively resolve the file share access issue, a systematic troubleshooting approach is necessary, starting with examining the permissions on the Windows Server and then investigating the configuration of the macOS High Sierra clients.

### **4.1 Utilizing the "Effective Access" Tool in Windows Server**

The **"Effective Access"** tool in Windows Server is invaluable for determining the actual permissions granted to a user or group on a specific resource, taking into account all applicable NTFS permissions, group memberships, and any explicit "Deny" settings 37. To use this tool for the specific subfolder in question, follow these steps:

1. Navigate to the subfolder in File Explorer.  
2. Right-click on the subfolder and select **Properties**.  
3. In the Properties window, go to the **Security** tab.  
4. Click the **Advanced** button.  
5. In the Advanced Security Settings window, navigate to the **Effective Access** tab.  
6. Click the **Select a user or group** link.  
7. In the Select User or Group dialog box, enter the username of one of the affected Domain Admins users and click **Check Names**, then click **OK** 37.  
8. Click the **View effective access** button 37.

The resulting list will display the actual permissions that the selected user has on the subfolder. Carefully examine this list for any unexpected "Deny" permissions. The tool also shows the cumulative effect of the user's individual permissions and the permissions granted to any groups they are members of 5. Pay close attention to whether the Domain Admins group itself or any other group the affected users belong to has an explicit "Deny" ACE on the subfolder. The "What if" feature within the Effective Access tab can also be used to simulate the effect of adding a user to a specific group, which can be helpful in understanding permission assignments 37. If the Effective Access tool shows that the users should indeed have access (i.e., no explicit "Deny" and sufficient "Allow" permissions), then the issue likely lies with the interaction between macOS High Sierra and the SMB protocol.

As an alternative to the GUI tool, the **Get-NTFSEffectiveAccess** cmdlet from the **NTFSSecurity** PowerShell module can be used to retrieve the effective permissions for a user on a file or folder from the command line 40. This might be useful for scripting or remote analysis. To use it, the module needs to be installed first using Install-Module \-Name NTFSSecurity. Then, the effective access can be checked using the command: Get-NTFSEffectiveAccess \-Path "\\\\path\\to\\UNC\\file" \-Account \<samaccountname\>.

Several **third-party tools** are also available that can provide more comprehensive reports on effective permissions, such as Netwrix Effective Permissions Reporting Tool and AccessEnum from Sysinternals 41.

### **4.2 Analyzing Windows Server Security Logs for Access Denied Errors**

Analyzing the Windows Server Security logs can provide valuable insights into failed access attempts 43. If auditing is not already enabled for file and folder access failures on the specific subfolder, it will need to be configured first 43. This can be done through Group Policy to enable the **"Audit object access"** policy 46. Additionally, auditing needs to be configured on the specific subfolder itself within its Security settings 43. When configuring the auditing entry for the affected users (or ideally the Domain Admins group), specify that you want to audit **failed** access attempts for actions like Read, Write, and Delete.

Once auditing is enabled, access the **Event Viewer** on the Windows Server (by running eventvwr.msc) and navigate to the **Security** logs 42. Filter the logs for events related to "Access Denied." While **Event ID 4625** typically indicates failed logon attempts, look for other relevant Event IDs related to object access, such as **4656** (A handle to an object was requested) or **4663** (An attempt was made to access an object) that show a failure status 42. Filter these events further by the affected user accounts and the path to the specific subfolder. Examining the details of these event logs can reveal the user account that attempted access, the type of access being attempted, and the reason for the failure. This information can be crucial in pinpointing whether the denial is occurring at the server level and potentially why.

### **4.3 Investigating Explicit Permissions on the Subfolder**

Directly examining the permissions set on the specific subfolder is essential. To do this:

1. Navigate to the subfolder in File Explorer.  
2. Right-click on the subfolder and select **Properties**.  
3. Go to the **Security** tab 1.

Here, you will see a list of users and groups that have been granted or denied permissions on the subfolder. Carefully review the entries, paying particular attention to any ACEs that explicitly deny access to the **Domain Admins** group or any other groups that the affected users might be members of, either directly or through nested group memberships. Remember that an explicit "Deny" ACE will override any "Allow" permissions 10. Note the order of the ACEs as well, although typically, an explicit "Deny" will take precedence regardless of its position in the list 14. Also, consider the possibility of nested group memberships. The affected users might be members of other groups that have explicit "Deny" permissions on the subfolder.

### **4.4 Examining macOS High Sierra Client Configurations**

Since the issue is specific to macOS High Sierra clients, their configuration needs to be thoroughly investigated.

Start by checking **Keychain Access** on one of the affected macOS clients (found in Applications \> Utilities) 29. Search for any saved passwords related to the Windows file share server. Try deleting these entries and then attempting to access the share again, which should prompt for credentials to be re-entered. This will ensure that any potentially outdated or corrupted cached credentials are not causing the issue 33.

Next, verify the **SMB client settings** on the macOS High Sierra client by examining the **/etc/nsmb.conf** file 21. This file might need to be created if it doesn't already exist. Open Terminal (Applications \> Utilities) and use a text editor like nano to create or edit the file. Based on research indicating potential SMB version compatibility issues with High Sierra, try adding or modifying the following line under a \[default\] section: protocol\_vers\_map=2 22. This setting forces the macOS client to use SMB version 2, which has been reported to resolve some issues. Another setting to consider, for testing purposes only (due to security implications), is disabling SMB signing by adding the line: signing\_required=no 21. Additionally, the setting file\_ids\_off=yes has been suggested as a potential workaround for some SMB-related problems 23. After making any changes to /etc/nsmb.conf, save the file (Ctrl+O in nano) and exit (Ctrl+X in nano), then reboot the macOS client for the changes to take effect.

Finally, check the **File Sharing preferences** on the macOS client (System Preferences \> Sharing \> File Sharing) 28. Ensure that File Sharing is turned on and that under Options, "Share files and folders using SMB" is checked. Verify that the user accounts being used to access the Windows share are selected under Windows File Sharing and that their passwords are correctly entered.

## **5\. Insights from Similar Scenarios**

The research material reveals several documented cases and forum discussions detailing similar permission or access issues when macOS High Sierra clients interact with Windows file shares over SMB 21. A recurring theme is the instability and potential bugs in macOS High Sierra's SMB implementation, often requiring specific client-side configurations to achieve reliable connectivity and proper permission handling.

Many users have reported success in resolving various SMB issues by **forcing the macOS client to use SMB version 2** via the /etc/nsmb.conf file 22. This suggests that the default SMB version negotiated between High Sierra and Windows Server 2019 might be the source of the problem. Issues related to **SMB signing** have also been frequently mentioned, with some users finding that disabling client-side signing resolves access or performance problems 21.

Furthermore, there are discussions about **incorrect permission inheritance or application** when files or folders are copied between macOS clients and Windows file shares over SMB, sometimes resulting in restrictive permissions being applied on the macOS side 52. While this is not the exact issue reported, it highlights the potential for inconsistencies in how macOS interprets and applies Windows file permissions.

The collective experience documented in these scenarios strongly indicates that the problem is likely related to a specific behavior or bug in macOS High Sierra's SMB client when interacting with Windows Server 2019 under certain permission configurations. The workarounds involving forcing SMB 2 and adjusting SMB signing settings appear to be the most relevant to the reported issue. Online forums and community discussions can be valuable resources for finding solutions to such platform-specific problems.

## **6\. Best Practices for File Share Permissions in Mixed-OS Environments**

To minimize the occurrence of file share permission issues in environments with both Windows and macOS clients, adhering to several best practices is recommended:

* **Principle of Least Privilege:** Always grant users only the minimum permissions necessary to perform their job functions 1. Avoid giving broad permissions like Full Control unless absolutely required.  
* **Using Security Groups:** Assign permissions to Active Directory security groups rather than individual users 1. This simplifies management, ensures consistency, and makes it easier to manage access when users' roles change. This is particularly important for managing access for privileged groups like Domain Admins.  
* **Clear Folder Structure:** Implement a well-organized and logical folder structure 56. This makes it easier to understand and manage permissions effectively.  
* **Consistent Naming Conventions:** Use clear and consistent naming conventions for shares, folders, and security groups 56. This improves clarity and reduces the risk of errors during permission assignments.  
* **Careful Use of Inheritance:** Leverage permission inheritance to simplify management, but be cautious when breaking inheritance 1. Understand the implications of disabling inheritance and ensure that explicit permissions are configured correctly.  
* **Regular Permission Audits:** Conduct periodic reviews and audits of file share permissions 1. This helps ensure that permissions remain appropriate and secure as user roles and data sensitivity evolve.  
* **Testing in Mixed Environments:** Thoroughly test file share access from both Windows and macOS clients after making any permission changes to ensure that access is as expected across all platforms.  
* **Documentation:** Maintain comprehensive documentation of the file share structure and the assigned permissions 7. This is crucial for troubleshooting and for onboarding new administrators.  
* **Consider Dedicated Admin Groups:** For highly sensitive shares, consider using dedicated administrative groups with specific permissions instead of relying solely on Domain Admins, especially in scenarios where UAC or cross-platform compatibility might introduce complexities 13.  
* **SMB Configuration Baselines:** Establish and document baseline SMB client configurations for macOS clients, tailored to the specific versions of macOS and Windows Server in use 26. This can help ensure consistent and reliable file sharing and address known compatibility issues.

## **7\. Advanced Troubleshooting and Potential Workarounds**

If the standard troubleshooting steps do not resolve the issue, consider these more advanced options:

* **Checking for Conflicting Group Policies:** Investigate if any Group Policies applied to the file server or the affected users are inadvertently modifying permissions on the subfolder or affecting the Domain Admins group membership in unexpected ways 15. Use the Group Policy Management Console (gpmc.msc) and the Resultant Set of Policy (rsop.msc) tool to analyze policy application.  
* **Considering Network Infrastructure:** While less likely to be the primary cause of a permission issue, briefly consider if there are any network-level problems affecting connectivity specifically for macOS High Sierra clients to this particular subfolder. This could involve checking network devices, DNS settings, and firewall rules.  
* **Alternative Protocols (If Applicable):** While SMB is the standard for Windows and macOS interaction, if there are specific needs or legacy systems involved, explore if alternative file sharing protocols like AFP 19) or NFS might be relevant for specific scenarios.  
* **Server Updates:** Ensure that the Windows Server 2019 is up to date with the latest service packs and security updates, as some updates might include fixes for SMB-related issues.  
* **macOS Updates:** Similarly, ensure that the macOS High Sierra clients are running the latest available updates for that specific operating system version 22. Software updates can often address bugs and improve compatibility.  
* **Workarounds for Known macOS Bugs:** If further research reveals specific, documented bugs in macOS High Sierra's SMB client that match the symptoms, implement any known workarounds. This might include specific nsmb.conf settings beyond protocol version and signing, or alternative methods of connecting to the share, such as using the server's IP address instead of its hostname for the initial connection 49.

## **8\. Conclusion**

The file share access denial experienced by a subset of Domain Admins on macOS High Sierra clients, despite having full control on the parent folder, points towards a nuanced issue likely stemming from the interaction between macOS's SMB client and the specific permissions configuration on the subfolder within Windows Server 2019\.

Based on the research and analysis, the most probable causes include:

* An explicit "Deny" permission inadvertently or intentionally set on the specific subfolder for the Domain Admins group or a group they belong to.  
* A compatibility issue or bug in macOS High Sierra's SMB implementation when interacting with the server under the current permission settings.  
* Outdated or incorrect credentials for the affected users cached within macOS Keychain Access.  
* Non-optimal SMB client settings on the macOS High Sierra clients.

To resolve this issue, the following actions are recommended:

1. **Immediately use the "Effective Access" tool** on the Windows Server 2019 for the affected Domain Admins users on the specific subfolder to definitively identify the permissions they are actually receiving. Pay close attention to any explicit "Deny" entries.  
2. **Examine the Security logs** on the Windows Server for any "Access Denied" errors related to the affected users and the specific subfolder when accessed from macOS High Sierra.  
3. **Thoroughly investigate the explicit permissions** set directly on the specific subfolder, looking for any ACEs that might be denying access to Domain Admins or related groups.  
4. **On the affected macOS High Sierra clients:**  
   * **Clear or update the SMB passwords** for the file share server in Keychain Access.  
   * **Create or modify the /etc/nsmb.conf file** to force the use of SMB version 2 (protocol\_vers\_map=2). Consider temporarily disabling SMB signing (signing\_required=no) for testing purposes, with caution regarding security implications. Reboot the client after making these changes.  
   * **Verify the File Sharing preferences** to ensure SMB sharing is enabled for the appropriate user accounts.

Implementing the best practices outlined in this report will help prevent similar issues in the future. If the problem persists after these steps, further investigation into specific macOS High Sierra SMB bugs and potential workarounds might be necessary, or escalation to Microsoft or Apple support channels could be considered.

#### **Works cited**

1. The Complete Guide to NTFS vs Share Permissions \- Netwrix Blog, accessed March 27, 2025, [https://blog.netwrix.com/ntfs-vs-share-permissions](https://blog.netwrix.com/ntfs-vs-share-permissions)  
2. PowerEdge: NTFS File Folder and Share Permissions in Windows | Dell US, accessed March 27, 2025, [https://www.dell.com/support/kbdoc/en-us/*********/understanding-file-and-folder-permissions-in-windows](https://www.dell.com/support/kbdoc/en-us/*********/understanding-file-and-folder-permissions-in-windows)  
3. Understanding File Server Permissions \- Comparitech, accessed March 27, 2025, [https://www.comparitech.com/net-admin/file-server-permissions/](https://www.comparitech.com/net-admin/file-server-permissions/)  
4. How to Share a Folder from Server to Client \- TSplus, accessed March 27, 2025, [https://tsplus.net/remote-access/blog/how-to-share-a-folder-from-server-to-client/](https://tsplus.net/remote-access/blog/how-to-share-a-folder-from-server-to-client/)  
5. Effective permissions: A comprehensive guide | Lacework, accessed March 27, 2025, [https://www.lacework.com/cloud-security-fundamentals/effective-permissions-a-comprehensive-guide](https://www.lacework.com/cloud-security-fundamentals/effective-permissions-a-comprehensive-guide)  
6. Enable or Disable Inherited Permissions for Objects in Windows | Tutorials, accessed March 27, 2025, [https://www.tenforums.com/tutorials/88305-enable-disable-inherited-permissions-objects-windows.html](https://www.tenforums.com/tutorials/88305-enable-disable-inherited-permissions-objects-windows.html)  
7. How to Configure Inherited Permissions for Files and Folders in Windows \- NinjaOne, accessed March 27, 2025, [https://www.ninjaone.com/blog/how-to-configure-inherited-permissions/](https://www.ninjaone.com/blog/how-to-configure-inherited-permissions/)  
8. How to setup a Windows File Server Share with Security Permissions \- YouTube, accessed March 27, 2025, [https://www.youtube.com/watch?v=Gm-jE\_4E7Y0](https://www.youtube.com/watch?v=Gm-jE_4E7Y0)  
9. how do you deactivate or change shared permissions inheritance? : r/sysadmin \- Reddit, accessed March 27, 2025, [https://www.reddit.com/r/sysadmin/comments/11aucfj/how\_do\_you\_deactivate\_or\_change\_shared/](https://www.reddit.com/r/sysadmin/comments/11aucfj/how_do_you_deactivate_or_change_shared/)  
10. Permission Precedence \- NTFS.com, accessed March 27, 2025, [https://www.ntfs.com/ntfs-permissions-precedence.htm](https://www.ntfs.com/ntfs-permissions-precedence.htm)  
11. Permission Propagation: Info and Tips \- Varonis, accessed March 27, 2025, [https://www.varonis.com/blog/permission-propagation](https://www.varonis.com/blog/permission-propagation)  
12. Explain “You do not have permission” error as a Domain Admin \- Please Work, accessed March 27, 2025, [https://pleasework.robbievance.net/explain-you-do-not-have-permission-error-as-a-domain-admin/](https://pleasework.robbievance.net/explain-you-do-not-have-permission-error-as-a-domain-admin/)  
13. AD permission issues when migrating file server : r/WindowsServer \- Reddit, accessed March 27, 2025, [https://www.reddit.com/r/WindowsServer/comments/164o531/ad\_permission\_issues\_when\_migrating\_file\_server/](https://www.reddit.com/r/WindowsServer/comments/164o531/ad_permission_issues_when_migrating_file_server/)  
14. How Share, NTFS Permissions and Inheritance Actually Work \- Sys-Manage, accessed March 27, 2025, [https://www.sys-manage.com/Blog/how-share-ntfs-permissions-and-inheritance-actually-work](https://www.sys-manage.com/Blog/how-share-ntfs-permissions-and-inheritance-actually-work)  
15. How to prevent Domain Admins from being denied access to network folders? \- Server Fault, accessed March 27, 2025, [https://serverfault.com/questions/286938/how-to-prevent-domain-admins-from-being-denied-access-to-network-folders](https://serverfault.com/questions/286938/how-to-prevent-domain-admins-from-being-denied-access-to-network-folders)  
16. "Access Denied" or other errors when you access or work with files and folders in Windows, accessed March 27, 2025, [https://support.microsoft.com/en-us/topic/-access-denied-or-other-errors-when-you-access-or-work-with-files-and-folders-in-windows-219af563-1953-ab4a-f17e-b0182755214e](https://support.microsoft.com/en-us/topic/-access-denied-or-other-errors-when-you-access-or-work-with-files-and-folders-in-windows-219af563-1953-ab4a-f17e-b0182755214e)  
17. Inherited permissions are not automatically updated when you move folders, accessed March 27, 2025, [https://learn.microsoft.com/en-us/troubleshoot/windows-server/windows-security/inherited-permissions-not-automatically-update](https://learn.microsoft.com/en-us/troubleshoot/windows-server/windows-security/inherited-permissions-not-automatically-update)  
18. Weird issue with file copying and permissions inheritance... in Windows 7 and 2012 R2 : r/sysadmin \- Reddit, accessed March 27, 2025, [https://www.reddit.com/r/sysadmin/comments/2syo8g/weird\_issue\_with\_file\_copying\_and\_permissions/](https://www.reddit.com/r/sysadmin/comments/2syo8g/weird_issue_with_file_copying_and_permissions/)  
19. Sharing Mac® Files on Windows® Servers A Technical Best Practices Whitepaper \- Acronis, accessed March 27, 2025, [http://dl.acronis.com/u/pdf/GLEZIP\_whitepaper3\_en-US.pdf](http://dl.acronis.com/u/pdf/GLEZIP_whitepaper3_en-US.pdf)  
20. Article | macOS 10.13 High Sierra Third Party Product Compatibility Info \- MacStrategy, accessed March 27, 2025, [https://www.macstrategy.com/article.php?183](https://www.macstrategy.com/article.php?183)  
21. How's SMB in High Sierra? \- Apple Support Communities, accessed March 27, 2025, [https://discussions.apple.com/thread/8086042](https://discussions.apple.com/thread/8086042)  
22. Anyone having SMB issues in High Sierra 10.13.2? : r/MacOS \- Reddit, accessed March 27, 2025, [https://www.reddit.com/r/MacOS/comments/7mi0sg/anyone\_having\_smb\_issues\_in\_high\_sierra\_10132/](https://www.reddit.com/r/MacOS/comments/7mi0sg/anyone_having_smb_issues_in_high_sierra_10132/)  
23. Why am I having issues establishing SMB access to Windows Server 2016 from macOS Catalina? \- Super User, accessed March 27, 2025, [https://superuser.com/questions/1515899/why-am-i-having-issues-establishing-smb-access-to-windows-server-2016-from-macos](https://superuser.com/questions/1515899/why-am-i-having-issues-establishing-smb-access-to-windows-server-2016-from-macos)  
24. Re: macOS High Sierra 10.13.2 \- Finder issues when copying files in mounted shares \- Jamf Nation, accessed March 27, 2025, [https://community.jamf.com/t5/jamf-pro/macos-high-sierra-10-13-2-finder-issues-when-copying-files-in/m-p/202502](https://community.jamf.com/t5/jamf-pro/macos-high-sierra-10-13-2-finder-issues-when-copying-files-in/m-p/202502)  
25. Mac Bug Connecting to Non-Apple SMB Shares \- Marriott Library, accessed March 27, 2025, [https://apple.lib.utah.edu/mac-bug-connecting-to-non-apple-smb-shares/](https://apple.lib.utah.edu/mac-bug-connecting-to-non-apple-smb-shares/)  
26. Adjust SMB Behaviour in macOS 10.13 and later \- CIT (UK), accessed March 27, 2025, [https://www.centralit-helpdesk.co.uk/index.php?pg=kb.page\&id=241](https://www.centralit-helpdesk.co.uk/index.php?pg=kb.page&id=241)  
27. High Sierra 10.13.3 File Sharing Issue "Connection Failed" \- Apple Support Community, accessed March 27, 2025, [https://discussions.apple.com/thread/8292542](https://discussions.apple.com/thread/8292542)  
28. Unable to access files with File Sharing in Mac OS High Sierra \- Apple Stack Exchange, accessed March 27, 2025, [https://apple.stackexchange.com/questions/312318/unable-to-access-files-with-file-sharing-in-mac-os-high-sierra](https://apple.stackexchange.com/questions/312318/unable-to-access-files-with-file-sharing-in-mac-os-high-sierra)  
29. Mac's having an issue accessing Windows Shares : r/macsysadmin \- Reddit, accessed March 27, 2025, [https://www.reddit.com/r/macsysadmin/comments/1eksf96/macs\_having\_an\_issue\_accessing\_windows\_shares/](https://www.reddit.com/r/macsysadmin/comments/1eksf96/macs_having_an_issue_accessing_windows_shares/)  
30. How to Use Keychain Access for Mac | Tutorial \- YouTube, accessed March 27, 2025, [https://www.youtube.com/watch?v=Og2RA10HW6U](https://www.youtube.com/watch?v=Og2RA10HW6U)  
31. Where does File Sharing store SMB passwords \- Ask Different \- Apple StackExchange, accessed March 27, 2025, [https://apple.stackexchange.com/questions/306151/where-does-file-sharing-store-smb-passwords](https://apple.stackexchange.com/questions/306151/where-does-file-sharing-store-smb-passwords)  
32. Everything You Need to Know About Keychain in macOS Sierra \- MacSales.com, accessed March 27, 2025, [https://eshop.macsales.com/blog/40209-everything-you-need-to-know-about-keychain-in-macos-sierra/](https://eshop.macsales.com/blog/40209-everything-you-need-to-know-about-keychain-in-macos-sierra/)  
33. SMB Credentials Cached in Sonoma, Can't Update : r/MacOS \- Reddit, accessed March 27, 2025, [https://www.reddit.com/r/MacOS/comments/1ay0vhx/smb\_credentials\_cached\_in\_sonoma\_cant\_update/](https://www.reddit.com/r/MacOS/comments/1ay0vhx/smb_credentials_cached_in_sonoma_cant_update/)  
34. How do I forget or change the username and password for file sharing? \- Ask Different, accessed March 27, 2025, [https://apple.stackexchange.com/questions/377124/how-do-i-forget-or-change-the-username-and-password-for-file-sharing](https://apple.stackexchange.com/questions/377124/how-do-i-forget-or-change-the-username-and-password-for-file-sharing)  
35. macOS High Sierra & Server 5.4 Part 4: File Sharing New Location \- YouTube, accessed March 27, 2025, [https://www.youtube.com/watch?v=KocMl2TNuOI](https://www.youtube.com/watch?v=KocMl2TNuOI)  
36. Set up SMB file sharing on Mac \- Apple Support, accessed March 27, 2025, [https://support.apple.com/guide/mac-help/set-up-smb-file-sharing-on-mac-mh14107/mac](https://support.apple.com/guide/mac-help/set-up-smb-file-sharing-on-mac-mh14107/mac)  
37. How to use Effective Access to check Windows file share permissions \- YouTube, accessed March 27, 2025, [https://www.youtube.com/watch?v=GnTML0rW6Gk](https://www.youtube.com/watch?v=GnTML0rW6Gk)  
38. learn.microsoft.com, accessed March 27, 2025, [https://learn.microsoft.com/en-us/troubleshoot/windows-server/windows-security/access-checks-windows-apis-return-incorrect-results\#:\~:text=If%20you're%20using%20an,AuthzInitializeContextFromSid](https://learn.microsoft.com/en-us/troubleshoot/windows-server/windows-security/access-checks-windows-apis-return-incorrect-results#:~:text=If%20you're%20using%20an,AuthzInitializeContextFromSid)  
39. How to evaluate effective permissions for resources on remote computers \- Windows Server, accessed March 27, 2025, [https://learn.microsoft.com/en-us/troubleshoot/windows-server/windows-security/access-checks-windows-apis-return-incorrect-results](https://learn.microsoft.com/en-us/troubleshoot/windows-server/windows-security/access-checks-windows-apis-return-incorrect-results)  
40. Check 'effective permissions', 'effective access' from command-line, Windows/NTFS, accessed March 27, 2025, [https://serverfault.com/questions/1002544/check-effective-permissions-effective-access-from-command-line-windows-ntf](https://serverfault.com/questions/1002544/check-effective-permissions-effective-access-from-command-line-windows-ntf)  
41. Effective Permissions Report with DAC \- JiJi AuditReporter, accessed March 27, 2025, [https://www.jijitechnologies.com/blogs/dynamic-access-control-effective-permissions.aspx](https://www.jijitechnologies.com/blogs/dynamic-access-control-effective-permissions.aspx)  
42. Top 11 NTFS Permissions Tools for Smarter Administration \- Netwrix Blog, accessed March 27, 2025, [https://blog.netwrix.com/2021/01/13/ntfs-permissions-tools/](https://blog.netwrix.com/2021/01/13/ntfs-permissions-tools/)  
43. How to Audit Failed Access Attempts to a Shared Folder | ADAudit Plus \- ManageEngine, accessed March 27, 2025, [https://www.manageengine.com/products/active-directory-audit/how-to/audit-failed-access-attempts-to-shared-folder.html](https://www.manageengine.com/products/active-directory-audit/how-to/audit-failed-access-attempts-to-shared-folder.html)  
44. How to enable file and folder access auditing in Windows Server \- ManageEngine, accessed March 27, 2025, [https://www.manageengine.com/products/active-directory-audit/kb/how-to/how-to-enable-file-and-folder-access-auditing-in-windows-server.html](https://www.manageengine.com/products/active-directory-audit/kb/how-to/how-to-enable-file-and-folder-access-auditing-in-windows-server.html)  
45. How To Enable File and Folder Auditing on Windows Server \- Lepide, accessed March 27, 2025, [https://www.lepide.com/how-to/enable-file-folder-access-auditing-windows-server.html](https://www.lepide.com/how-to/enable-file-folder-access-auditing-windows-server.html)  
46. How to Enable File Share Auditing | Windows Server 2019 / 2022 \- YouTube, accessed March 27, 2025, [https://www.youtube.com/watch?v=rpXet2AxVeY](https://www.youtube.com/watch?v=rpXet2AxVeY)  
47. How to enable File Share Auditing on Windows Server \- YouTube, accessed March 27, 2025, [https://www.youtube.com/watch?v=rMRc9RVTEx0](https://www.youtube.com/watch?v=rMRc9RVTEx0)  
48. Complete Guide to Windows File System Auditing \- Varonis, accessed March 27, 2025, [https://www.varonis.com/blog/windows-file-system-auditing](https://www.varonis.com/blog/windows-file-system-auditing)  
49. Can't connect from macOS High Sierra to Windows 10 share on home network, accessed March 27, 2025, [https://apple.stackexchange.com/questions/302490/cant-connect-from-macos-high-sierra-to-windows-10-share-on-home-network](https://apple.stackexchange.com/questions/302490/cant-connect-from-macos-high-sierra-to-windows-10-share-on-home-network)  
50. Unable to access SMB shares using network account credentials \- Apple Communities, accessed March 27, 2025, [https://discussions.apple.com/thread/*********](https://discussions.apple.com/thread/*********)  
51. Granting macOS permissions for Zoom, accessed March 27, 2025, [https://support.zoom.com/hc/en/article?id=zm\_kb\&sysparm\_article=KB0064868](https://support.zoom.com/hc/en/article?id=zm_kb&sysparm_article=KB0064868)  
52. Mac SMB Share Permissions Issue \- Mac \- EduGeek.net, accessed March 27, 2025, [https://www.edugeek.net/forums/mac/198852-mac-smb-share-permissions-issue.html](https://www.edugeek.net/forums/mac/198852-mac-smb-share-permissions-issue.html)  
53. macos \- OSX \- File Sharing \- Permission problems \- Ask Different \- Apple StackExchange, accessed March 27, 2025, [https://apple.stackexchange.com/questions/340690/osx-file-sharing-permission-problems](https://apple.stackexchange.com/questions/340690/osx-file-sharing-permission-problems)  
54. 10.13.3 smb permissions inheritance lost \- Apple Communities, accessed March 27, 2025, [https://discussions.apple.com/thread/8337384](https://discussions.apple.com/thread/8337384)  
55. Can't connect to SMB share after Sierra upgrade \- Apple Stack Exchange, accessed March 27, 2025, [https://apple.stackexchange.com/questions/256151/cant-connect-to-smb-share-after-sierra-upgrade](https://apple.stackexchange.com/questions/256151/cant-connect-to-smb-share-after-sierra-upgrade)  
56. Top 10 Windows File Server Best Practices \- Active Directory Pro, accessed March 27, 2025, [https://activedirectorypro.com/windows-file-server-best-practices/](https://activedirectorypro.com/windows-file-server-best-practices/)  
57. Best practices: sharing folders, NTFS+share permissions and the Everyone permissiion, accessed March 27, 2025, [https://techcommunity.microsoft.com/discussions/windowsserver/best-practices-sharing-folders-ntfsshare-permissions-and-the-everyone-permissiio/673125](https://techcommunity.microsoft.com/discussions/windowsserver/best-practices-sharing-folders-ntfsshare-permissions-and-the-everyone-permissiio/673125)  
58. Addressing Compatibility Issues with Active Directory in Mixed OS Environments \- Reintech, accessed March 27, 2025, [https://reintech.io/blog/active-directory-compatibility-mixed-os-environments](https://reintech.io/blog/active-directory-compatibility-mixed-os-environments)