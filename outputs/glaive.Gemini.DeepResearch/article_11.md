# **Optimizing Lattice Structures for FDM-Printed Robotic Nodes in Swarm Robotics**

## **Introduction: The Critical Role of Optimized Lattice Structures in Swarm Robotics Nodes**

Swarm robotics, a burgeoning field within artificial intelligence, focuses on the collaborative control of a large number of relatively simple robots operating in a decentralized manner to achieve complex tasks 1. This bio-inspired approach draws parallels with natural swarm systems such as social insect colonies, emphasizing the collective intelligence and behavior that emerge from the local interactions of individual agents 1. The design and capabilities of each individual robotic node within a swarm are paramount to the overall performance, robustness, flexibility, and scalability of the entire system 1. Inefficient or structurally weak individual robots can negatively impact the swarm's ability to perform its intended functions effectively.

One promising avenue for enhancing the design of robotic nodes, particularly in applications demanding lightweighting and high strength, is the utilization of lattice structures 2. These intricate, repeating patterns can significantly reduce the amount of material required for a component, thereby decreasing its weight without necessarily compromising its mechanical properties 4. Indeed, lattice structures often exhibit a superior strength-to-weight ratio compared to their solid counterparts, a critical attribute for mobile robots where weight directly impacts energy consumption and maneuverability 2. The intersection of advanced material design, through the use of lattice structures, and advanced manufacturing techniques, such as Fused Deposition Modeling (FDM) 3D printing, presents a unique opportunity to create highly optimized robotic components tailored for specific applications like swarm robotics. FDM's layer-by-layer fabrication process enables the creation of complex geometries like lattice structures that are often difficult or impossible to achieve using traditional manufacturing methods 13.

This report addresses the specific challenges associated with optimizing the lattice structure of a 3D printed robotic node for a swarm robotics application. The primary objectives are to maximize the strength-to-weight ratio of the node, minimize the amount of support material required during the FDM printing process, and seamlessly integrate connectors for both inter-robot communication and power transfer. Furthermore, this optimization must consider the inherent limitations of FDM printing technology and the unique demands posed by a swarm robotics environment, such as the need for modularity, robustness, and reliable interconnections between individual robots. This report will explore various lattice topologies, analyze their mechanical properties, investigate the limitations of FDM printing and strategies for support minimization, discuss methods for connector integration, review relevant research in robotics, consider the specific needs of swarm robotics, examine computational tools available for design and optimization, and finally, discuss best practices for testing and validating the mechanical performance of the designed structures.

## **Exploring Lattice Structure Fundamentals for Superior Strength-to-Weight Performance**

Lattice structures, at their core, are cellular materials characterized by a periodic arrangement of unit cells repeated to fill a given space 2. These structures can be broadly categorized based on their constituent elements, such as beam-based (or strut-based) lattices, surface-based lattices utilizing triply periodic minimal surfaces (TPMS), planar lattices formed by extruding a 2D pattern, and stochastic lattices with a more randomized arrangement 15. Within these categories exist numerous unit cell designs, each offering a unique set of mechanical properties. Examples of common unit cells include simple cubic, body-centered cubic (BCC), face-centered cubic (FCC), gyroid, diamond, tetrahedral, and honeycomb structures 7.

The mechanical properties of lattice structures, including their strength, stiffness, and density, are heavily influenced by the geometry and arrangement of their unit cells 5. For instance, the way beams or surfaces are connected within the unit cell dictates how the structure will respond to applied loads. Lattice structures can exhibit either stretch-dominant or bending-dominant deformation behavior 2. Stretch-dominant structures, where the primary deformation mechanism involves the stretching or compression of the constituent members, generally possess higher strength compared to bending-dominant structures, where deformation occurs primarily through the bending of these members 2. The stability of a lattice structure, and thus its dominant deformation mode, can be assessed using Maxwell's stability criterion, which relates the number of beams and joints in the structure 2.

The strength-to-weight ratio, a key metric for optimizing robotic nodes, varies significantly across different lattice types 2. Research has shown that certain lattice configurations can indeed offer a better strength-to-weight ratio than solid materials 2. For example, a study on Inconel718 lattice structures manufactured using powder bed fusion revealed significant differences in strength-to-weight ratio among various designs, with models featuring only angular beams or only interior beams along the unit cell's body diagonal exhibiting superior performance compared to solid specimens 2. Another study comparing strut-based and surface-based lattice structures found that a specific beam lattice structure (BLS\_2) demonstrated the highest strength-to-weight ratio in flexural loading 24. These findings underscore that the selection of the appropriate lattice topology is critical for achieving optimal mechanical performance with minimal weight. The relative density of the lattice, which is the ratio of the volume of solid material to the total volume of the structure, also plays a crucial role in determining its mechanical properties; generally, higher relative densities lead to increased strength and stiffness but also greater weight 2.

The optimal lattice structure for maximizing strength-to-weight ratio is not universal and depends heavily on the specific loading conditions the robotic node will experience and the overall requirements of the application. Different lattice topologies exhibit varying mechanical responses under different types of stress, such as compression, flexure, and torsion 2. For instance, stretch-dominant structures are generally more effective at resisting axial loads, making them suitable for applications where the primary stresses are tensile or compressive. Conversely, bending-dominant structures might be a better choice for applications requiring higher energy absorption or flexibility 2. The intended function of the robotic node within the swarm will therefore dictate the primary types of loads it will encounter, directly influencing the most appropriate lattice selection.

Surface-based lattices, particularly gyroid structures, have emerged as promising candidates for robotic applications due to their often superior balance of mechanical properties and manufacturability 25. Research indicates that gyroid lattices possess high stiffness, strength, and energy absorption capabilities 25. Furthermore, their continuous, smoothly curved surfaces can offer advantages in additive manufacturing processes like FDM, potentially being self-supporting in certain orientations and facilitating the removal of un-sintered powder in powder-based printing methods 27. This combination of robust mechanical performance and relatively ease of fabrication makes gyroid lattices an attractive option for creating complex and optimized robotic components.

**Table 1: Comparative Analysis of Common Lattice Structures**

| Lattice Type | Typical Strength-to-Weight Ratio | Stiffness Characteristics | Dominant Deformation Mode | Suitability for FDM Printing | Relevant Snippet IDs |
| :---- | :---- | :---- | :---- | :---- | :---- |
| Simple Cubic | Low to Medium | Can offer high stiffness/elasticity | Bending | Moderate | 17, 17, 19 |
| BCC | Medium to High | High stiffness | Stretch | Moderate | 17, 17, 22, 73 |
| FCC | Medium to High | High stiffness, ductile | Stretch | Moderate | 7, 73, 19, 74 |
| Gyroid | High | Good all-around properties | Mixed | Good | 15, 16, 25, 26, 27, 32 |
| Diamond | High | High stiffness | Stretch | Moderate | 16, 31, 18 |
| Tetrahedral | High | High stiffness | Stretch | Moderate | 75, 28, 76, 77, 8, 18 |
| Honeycomb | Medium | High directional stiffness | Bending | Good (planar) | 15, 6, 16, 7 |

## **Addressing the Constraints of Fused Deposition Modeling (FDM) in Lattice Design**

Fused Deposition Modeling (FDM) is an additive manufacturing process that builds parts layer by layer by extruding a thermoplastic filament through a heated nozzle 13. While offering advantages in terms of cost-effectiveness and material versatility, FDM also presents inherent limitations that must be carefully considered when designing lattice structures, particularly for complex geometries. These limitations include constraints related to overhang angles, bridging capabilities, and the material properties of the printed parts 13.

The layer-by-layer deposition process of FDM means that unsupported overhangs, features that extend outward without underlying support from the previous layer, can sag or deform if the angle exceeds a critical threshold, typically around 45 degrees relative to the build platform 14. Beyond this critical overhang angle, support structures are generally required to provide a foundation for the subsequent layers 14. Similarly, bridging, the ability to print horizontal spans between two supported points, is limited by the material's melt viscosity and cooling rate 35. Exceeding the maximum bridging distance can lead to sagging or even failure of the unsupported span 35. Furthermore, FDM-printed parts often exhibit anisotropic material properties, meaning their strength and stiffness vary depending on the direction of the applied load relative to the printing layers 34. This anisotropy arises from the nature of the layer-by-layer bonding, which might be weaker than the material's inherent strength.

These FDM limitations have a direct impact on the selection and design parameters of lattice structures for 3D printing. Certain complex lattice topologies with intricate features or unfavorable orientations might be difficult to print successfully without extensive support structures 35. The design must carefully consider the angles of the struts and the distances between nodes to ensure they remain within the self-supporting capabilities and bridging limits of the FDM process 37. If supports are deemed necessary for certain lattice designs or orientations, it is crucial to design the part with accessibility in mind to facilitate their removal after printing 35.

Several strategies can be employed to mitigate the common FDM printing challenges in lattice structures. Designing with self-supporting angles, ideally less than 45 degrees where possible, can significantly reduce or eliminate the need for supports 14. Strategically orienting the part on the build platform can also minimize the occurrence of overhangs and maximize the use of self-supporting features 8. For instance, rotating a cubic lattice structure by 45 degrees about an edge can transform horizontal members into angled, self-supporting struts 8. Additionally, optimizing printing parameters such as layer height, print speed, and, if applicable to solid regions within the design, infill density can influence the quality and structural integrity of the printed lattice 34. Finer layer heights might improve the resolution of small lattice features but could also increase printing time.

The relationship between the chosen lattice structure and the specific FDM printing parameters is critical for achieving the desired mechanical performance. A lattice structure that theoretically promises a high strength-to-weight ratio might underperform in practice if it is not designed and printed correctly using FDM. The layer-by-layer fabrication inherent to FDM can introduce defects such as voids or weak interlayer bonding, particularly in intricate lattice structures with small feature sizes or unfavorable printing orientations 34. These manufacturing imperfections can significantly reduce the actual strength and stiffness of the printed part compared to predictions from simulations based on ideal geometries. Therefore, the design process must always consider the constraints and characteristics of the chosen manufacturing method, and printing parameters must be carefully optimized for the specific lattice topology to ensure the final robotic node meets the required performance specifications.

Despite these limitations, the accessibility and cost-effectiveness of FDM technology make it a highly practical choice for a wide range of applications, including the development of swarm robotics systems 38. FDM printers are relatively inexpensive and readily available, and a broad spectrum of thermoplastic materials with varying properties can be used 38. For swarm robotics, where factors such as cost and ease of fabrication are often paramount, FDM can be a particularly suitable manufacturing method, provided that the lattice design is thoughtfully tailored to its inherent constraints. By carefully considering overhang angles, bridging distances, and build orientation, and by optimizing printing parameters, engineers can effectively leverage FDM to create functional and robust lattice-structured robotic nodes for swarm applications.

## **Minimizing Support Material Requirements in FDM Printing of Complex Lattice Geometries**

Minimizing the need for support material in FDM printing of complex lattice geometries is crucial for reducing material waste, shortening print times, and improving the surface finish of the final part 35. Several design methodologies, build orientation strategies, and slicing techniques can be employed to achieve this goal 8.

One effective approach involves selecting or designing lattice unit cells that are inherently self-supporting 8. Nature often provides inspiration for such designs. For instance, lattice structures inspired by the morphology of sea urchins have been shown to be printable without supports using FDM 2. These shell-shaped structures possess inherent stability and load-bearing capabilities that minimize the need for external support during the printing process 37. Similarly, unit cell geometries with multi-fold rotational symmetry, such as those found in crystallographic structures, can be designed to be self-supporting due to their balanced and interconnected nature 40. Surface-based lattices like gyroids, with their continuous and gradually changing curvatures, can also reduce the requirement for supports compared to sharp-angled beam-based lattices 18. Hybrid lattice structures, which combine different unit cell types within a single design, can also be strategically employed to achieve self-support while simultaneously enhancing mechanical properties 19.

Optimizing the build orientation of the robotic node on the FDM printer's build platform is another critical strategy for minimizing support usage 8. By carefully rotating the part, engineers can often orient features in a way that reduces the number and extent of overhangs requiring support. As previously mentioned, rotating a cubic truss structure by 45 degrees can make all its members self-supporting 8. Similarly, for more complex lattice geometries, analyzing the design to identify the orientation that presents the fewest steep overhangs or long bridges can significantly decrease the need for support material 8. The choice of layer height during printing can also indirectly impact support requirements. While finer layer heights can improve the resolution of intricate lattice features, they might also reduce the material's ability to bridge gaps, potentially increasing the need for supports in certain areas.

Advanced slicing techniques offered by some specialized software can also contribute to support minimization. These techniques might involve adaptive slicing, where the layer height is varied across different regions of the part to optimize for both detail and self-support. Some software packages also offer sophisticated algorithms for generating optimized support structures that use less material and are easier to remove than traditional supports 44. By analyzing the geometry of the lattice structure, these tools can identify critical areas that require support and generate only the necessary structures, minimizing material usage and post-processing effort. In some cases, multi-plane FDM printing, which utilizes robotic arms to print in non-horizontal layers, can also be explored to create complex, support-free lattice structures by strategically orienting the deposition path 49.

Designing for additive manufacturing (DfAM) principles, with a specific focus on self-support, is therefore paramount for achieving efficient and cost-effective production of lattice structures using FDM. Minimizing support material directly translates to reduced material consumption, faster printing times, and less post-processing work, ultimately leading to lower manufacturing costs and a more streamlined production process 35. By integrating DfAM considerations early in the design phase, engineers can select or modify lattice structures and orient parts in a manner that inherently reduces or eliminates the need for supports, maximizing the benefits of FDM for creating optimized robotic nodes.

Furthermore, the study of bio-inspired designs offers valuable insights into creating efficient and self-supporting lattice structures. Nature has, through evolutionary processes, already optimized many structural forms for strength and material efficiency 2. Natural structures such as honeycombs, bones, and sea urchin shells exhibit remarkable strength-to-weight ratios while often utilizing minimal material and employing self-supporting architectures 2. By carefully examining and understanding the underlying principles behind these natural designs, researchers and engineers can derive inspiration for novel lattice geometries and design strategies that are inherently well-suited for additive manufacturing processes like FDM, often leading to structures that require minimal or no support during printing.

## **Strategies for Integrating Connectors into 3D Printed Lattice Structures**

Integrating connectors, both physical and electrical, directly into the 3D printed lattice structure of a robotic node is essential for facilitating modularity, inter-robot communication, and power transfer in a swarm robotics application 51. Several design principles and methods can be employed to achieve this seamless integration while maintaining the structural integrity of the lattice.

For embedding physical connectors, such as those needed for mechanical attachment between robotic nodes or to external components, the lattice structure can be designed with specific features like pockets or bosses to accommodate standard connectors such as nuts or threaded inserts 51. These features can be incorporated into the CAD model of the lattice before printing, ensuring a precise fit for the chosen connectors. Snap-fit or press-fit connections can also be designed directly into the lattice structure, allowing for tool-less assembly and disassembly of robotic nodes 51. For applications requiring more robust and wear-resistant connections, threaded inserts, which can be either heat-set or screw-to-expand types, can be embedded into designated locations within the lattice after printing 50. When designing for connector integration, it is crucial to consider the manufacturing tolerances of the 3D printer to ensure a secure and functional fit without being too tight or too loose 52.

Incorporating electrical connectors for communication and power transfer can be achieved through various methods, including embedding connectors during the printing process, surface mounting them onto the lattice nodes, or designing internal channels within the lattice struts for routing wires to external or embedded connectors 55. Embedding electrical connectors directly into the lattice structure during printing can be done by pausing the print at a specific layer height, placing the connector in the designated cavity, and then resuming the print to encapsulate it within the lattice 52. This method can provide a secure and integrated connection. Smaller electrical connectors can also be surface-mounted onto flat faces or специально designed pads on the lattice nodes using adhesives or small fasteners. Another approach involves designing hollow channels within the lattice struts that can be used to route wires internally, protecting them from damage and maintaining a clean aesthetic 60. These internal wires can then connect to either embedded or surface-mounted electrical connectors. Furthermore, advancements in materials science have led to the development of conductive filaments and inks that can be used with 3D printing to create integrated electrical pathways directly within the lattice structure, potentially eliminating the need for separate wires and connectors in some applications 12.

Ensuring the structural integrity and functionality of integrated connectors is paramount for the reliable operation of the swarm robotic nodes. The lattice structure around the connector should be carefully designed to maintain overall strength and avoid the creation of stress concentrations that could lead to failure under load 31. For embedded connectors, methods such as using adhesives or designing mechanical interlocks within the cavity can help to secure the connector in place and prevent it from pulling out under load 51. When selecting electrical connectors for integration, it is essential to choose components that meet the specific electrical and mechanical demands of the application, considering factors such as voltage and current ratings, signal integrity requirements, and resistance to vibration and wear 55.

Integrating connectors directly into the 3D printed lattice structure offers several significant advantages over post-assembly attachment. It can lead to a reduction in assembly time and labor, as the connectors are incorporated during the manufacturing process itself. Embedding connectors can also contribute to weight reduction by minimizing the need for additional fastening components. Furthermore, internal routing of wires within the lattice can enhance the reliability and robustness of the system by protecting the wires from external damage. The choice of connector integration method will ultimately depend on a variety of factors, including the size and type of connector required, the material used for the lattice structure, the specific capabilities of the 3D printing technology being employed, and the desired strength and durability of the connection in the context of the swarm robotics application.

## **Lattice Structures in Robotics: A Review of Existing Research and Applications**

The application of lattice structures in the field of robotics is a growing area of research, with numerous studies demonstrating their effectiveness in enhancing the performance and functionality of robotic components 65. Case studies have explored the use of lattice structures in various robotic systems, including robotic arms, soft robots, and even entire robot bodies, highlighting the benefits of lightweighting, improved strength-to-weight ratios, and the ability to achieve specific functional characteristics.

Research has shown that incorporating lattice structures into the design of robotic arms can significantly reduce their weight, leading to improved energy efficiency and faster movements without sacrificing structural integrity 65. Topology optimization techniques are often employed in conjunction with lattice structures to further refine the design and maximize the strength-to-weight ratio for specific loading conditions encountered by the arm 65. In the realm of soft robotics, lattice structures are proving to be particularly valuable for creating robots with variable stiffness and the ability to perform complex and nuanced movements 66. By carefully designing the geometry and density of the lattice, engineers can control the flexibility and rigidity of different parts of the soft robot, enabling it to adapt to various tasks and environments 66.

Bio-inspired lattice designs are also gaining traction in robotics, drawing inspiration from the efficiency and robustness of natural structures 2. For example, the internal structure of bone, with its porous yet strong lattice network, has inspired designs for lightweight and load-bearing robotic components 2. Similarly, the structure of bamboo, with its segmented and reinforced hollow tubes, has informed the design of lattice structures with enhanced bending resistance and energy absorption capabilities 40. These bio-inspired designs often exhibit a remarkable balance of strength and lightweighting, making them well-suited for various robotic applications.

Performance evaluations of lattice structures in robotic systems have consistently demonstrated their effectiveness in achieving significant weight reductions while maintaining or even enhancing strength and stiffness 65. Studies have quantified the improvements in strength-to-weight ratio, load-bearing capacity, and energy absorption achieved through the use of different lattice topologies in robotic components. Furthermore, lattice structures can offer additional functional advantages in robotic systems, such as improved heat dissipation due to their high surface area 4 and enhanced shock absorption for protecting delicate components 6.

The increasing body of research on the application of lattice structures in robotics underscores their potential to revolutionize the design and performance of various robotic systems. From providing structural support in rigid robots to enabling novel functionalities in soft robots, lattice structures offer a versatile and effective approach to addressing key design challenges in the field. The continued exploration of different lattice topologies, manufacturing techniques, and application areas promises to further expand the role of these innovative materials in the future of robotics. The insights gained from studying bio-inspired designs are particularly valuable, as they provide a pathway to creating robotic systems with performance characteristics that have been optimized through natural selection over millennia.

## **Swarm Robotics Application Specifics and Their Influence on Lattice Node Design**

The specific requirements of a swarm robotics application exert a significant influence on the design choices for the individual robotic nodes, including the optimization of their lattice structures and the integration of connectors 1. Key considerations for swarm robots include modularity, scalability, robustness, and the reliable exchange of information and power between individual units.

Modularity is a crucial aspect of swarm robotics, as it allows for the easy assembly, repair, and reconfiguration of the swarm 1. The lattice structure of the robotic node can be designed to facilitate modularity by incorporating standardized connection points that enable robots to easily attach to and detach from each other or to external modules 51. The design should also consider scalability, ensuring that the chosen lattice structure and connection mechanisms are effective for swarms ranging from a few units to potentially hundreds or thousands 1.

Robustness and resilience are paramount in swarm robotics, as individual robots may encounter failures or damage during operation 1. The lattice structure can contribute to the overall robustness of the robotic node by providing a lightweight yet strong frame that can withstand impacts and stresses encountered in the operating environment 7. The interconnected nature of a swarm also provides a degree of fault tolerance, as the failure of one or more individual robots should not necessarily cripple the entire swarm's ability to perform its task 1. The design of the lattice structure can potentially enhance this fault tolerance by providing alternative load paths and preventing localized damage from propagating throughout the entire node.

Optimizing inter-robot communication and power transfer is essential for the coordinated behavior of a swarm 1. The design of the robotic node must accommodate connectors that enable reliable data exchange between neighboring robots, facilitating the sharing of sensor information, task assignments, and coordination commands 55. Depending on the specific application and swarm topology, these communication connectors might need to support various protocols and communication ranges, potentially including both physical connections and interfaces for wireless communication. Similarly, connectors for power transfer are necessary to distribute energy throughout the swarm, potentially allowing robots to share power or connect to a central power source 55. The lattice structure must be designed to accommodate these specific connector types and ensure their reliable function in the dynamic environment of a swarm. The choice of communication connectors and power transfer mechanisms will also be influenced by the swarm topology, which dictates how individual robots are interconnected and how information and resources flow throughout the system 70. For instance, a mesh topology, where each node connects to multiple neighbors, might require a different set of connectors compared to a ring topology, where each node is connected to only two adjacent neighbors 70.

The unique demands of swarm robotics necessitate a holistic approach to the design of individual robotic nodes. The optimization of the lattice structure for strength-to-weight ratio and minimal support material must be balanced with the need for modularity, robustness, and reliable inter-robot connectivity. Careful consideration of these application-specific requirements will ensure that the final design of the FDM-printed robotic node is well-suited for effective operation within a swarm.

## **Computational Tools and Software for Topology Optimization and Lattice Generation**

Computational tools and specialized software play a critical role in the design and optimization of complex lattice structures for robotic applications 14. These tools enable engineers to explore a vast design space, predict the mechanical performance of different lattice configurations, and generate optimized geometries that meet specific performance requirements and manufacturing constraints.

Topology optimization software is particularly relevant for designing lattice structures with high strength-to-weight ratios 44. This approach uses computational algorithms to determine the optimal distribution of material within a defined design space, subject to specific loads, constraints, and performance objectives 44. Several software packages, such as Altair HyperWorks (with its OptiStruct module), Ansys Additive Suite, and COMSOL Multiphysics, offer powerful topology optimization capabilities that can be used to generate lightweight and structurally efficient designs that can then be realized using lattice structures 44. These tools allow engineers to define the loading conditions the robotic node will experience and then automatically generate an optimized material layout that maximizes stiffness or strength while minimizing weight.

Specialized lattice generation software provides a comprehensive set of tools for creating intricate and customizable lattice structures for additive manufacturing 14. Packages like nTop, Materialise 3-matic, and Altair Sulis are specifically designed for this purpose, offering features such as one-click lattice creation from various unit cell types, precise control over lattice parameters like cell size and density, and the ability to create complex lattice geometries with smooth transitions and variable thickness 46. Importantly, many of these software solutions also incorporate design rules and considerations for FDM printing, such as minimum feature sizes and self-supporting angle constraints 14. This integration of design for additive manufacturing (DfAM) principles helps ensure that the generated lattice structures are actually printable and achieve the desired performance when fabricated using FDM. In addition to specialized software, some general-purpose CAD software packages, such as Autodesk Fusion 360 and NetFabb, also offer basic lattice generation tools that can be useful for less complex designs 6.

Simulation and analysis tools, particularly Finite Element Analysis (FEA) software like ANSYS and Abaqus, are essential for predicting the mechanical behavior of designed lattice structures under various loading conditions 2. By creating virtual models of the lattice structure and applying simulated loads and boundary conditions, engineers can use FEA to analyze the stress and strain distribution within the design, predict its strength and stiffness, and identify potential failure modes before any physical prototypes are manufactured 2. This capability is crucial for iterating on designs and optimizing the lattice structure for the specific requirements of the swarm robotics application.

The availability and sophistication of these computational tools have significantly empowered engineers to design and optimize complex lattice structures for robotic applications. By leveraging topology optimization, lattice generation software, and FEA simulation, it is possible to create highly efficient and functional robotic nodes tailored to the specific demands of swarm robotics, while also considering the constraints and capabilities of the FDM printing process.

## **Best Practices for Testing and Validating the Mechanical Performance of 3D Printed Lattice Structures**

Experimental validation through mechanical testing is a critical step in the process of designing and optimizing 3D printed lattice structures for swarm robotic nodes 29. While computational tools provide valuable predictions of mechanical performance, physical testing is necessary to confirm these predictions and to identify any discrepancies that may arise due to manufacturing imperfections or limitations in the simulation models.

Several established methodologies exist for mechanically testing lattice structures, including compression tests, tension tests, and flexural tests 29. Compression tests are commonly used to evaluate the stiffness and strength of lattice structures under compressive loads, which are often relevant for robotic nodes that might experience contact forces or support loads 29. Tension tests can be performed to determine the tensile strength and elongation of the lattice material, while flexural tests, such as three-point bending, are used to assess the bending strength and stiffness, which might be important for lattice structures used in flexible or articulating parts of a robot 2.

From the data obtained during these mechanical tests, key mechanical properties such as Young's modulus (a measure of stiffness), yield strength (the point at which permanent deformation begins), ultimate strength (the maximum stress the material can withstand), and toughness (the energy absorbed before fracture) can be calculated 30. Additionally, the durability and fatigue life of lattice structures, which are important for robotic nodes expected to undergo repeated movements or loads, can be evaluated through cyclic loading tests. Observing and analyzing the failure modes of the lattice structures during testing can also provide valuable insights into their structural behavior and potential weaknesses 29.

When integrating connectors into the lattice structure, specific testing procedures should be implemented to validate their performance. For physical connectors, pull-out tests can be used to assess the mechanical strength of embedded nuts or threaded inserts, ensuring they can withstand the forces expected during operation or assembly 51. For integrated electrical connectors, testing should go beyond simply verifying their initial conductivity. It is important to evaluate their reliability under various environmental conditions that might be encountered by the swarm robots, such as vibration, temperature fluctuations, and humidity. This might involve subjecting the connectors to cyclic loading, temperature cycling, and humidity tests to ensure they maintain a reliable electrical connection over time. Furthermore, it is crucial to assess how the integration of connectors impacts the structural integrity of the surrounding lattice structure, ensuring that the presence of the connector does not significantly weaken the robotic node's ability to withstand the expected loads.

Experimental validation through rigorous mechanical testing is therefore an indispensable part of the optimization process for 3D printed lattice structures in swarm robotics. It provides the necessary data to confirm simulation results, identify potential issues related to the manufacturing process or design, and ultimately ensure the reliability and performance of the final robotic nodes in their intended application.

## **Conclusion and Comprehensive Recommendations for Optimizing Lattice Structures in FDM-Printed Swarm Robotic Nodes**

The optimization of lattice structures for FDM-printed robotic nodes in swarm robotics requires a multifaceted approach that considers the interplay between lattice topology, FDM printing constraints, connector integration, and the specific demands of swarm applications. This report has explored these critical aspects, providing insights and guidance for engineers and researchers in this field.

Based on the analysis presented, the following comprehensive recommendations are offered for optimizing lattice structures in FDM-printed swarm robotic nodes:

1. **Lattice Topology Selection:** Choose lattice topologies that offer a high strength-to-weight ratio while being amenable to FDM printing. Surface-based lattices like gyroids and certain stretch-dominant beam lattices (e.g., BCC, tetrahedral) show promise. Consider the specific loading conditions the node will experience when selecting the most appropriate lattice type.  
2. **Design for Minimal Support Material:** Prioritize designing with self-supporting angles (ideally \< 45 degrees) and consider bio-inspired lattice designs. Optimize the build orientation on the FDM platform to minimize overhangs. Utilize lattice generation software that incorporates FDM design rules.  
3. **Connector Integration:** Design pockets or bosses for physical connectors, considering appropriate tolerances. Explore embedding or surface mounting electrical connectors, and consider internal routing of wires within lattice struts. Ensure the structural integrity around integrated connectors is maintained.  
4. **Computational Tools:** Employ topology optimization software to guide the design of efficient lattice structures. Utilize specialized lattice generation software to create complex geometries while considering FDM constraints. Use FEA software to simulate and analyze the mechanical performance of the designed lattices.  
5. **Testing and Validation:** Conduct thorough mechanical testing (compression, tension, flexure) to validate the predicted performance of printed lattice structures. Assess strength, stiffness, and durability. Specifically test the mechanical and electrical reliability of integrated connectors.

Potential future research directions in this area include the development of novel lattice unit cell designs specifically optimized for FDM printing and swarm robotics requirements. Further investigation into advanced materials and conductive filaments for integrated electrical functionality within lattice structures is also warranted. Exploring adaptive lattice structures with variable stiffness capabilities could also significantly enhance the functionality of swarm robots. Finally, the development of standardized testing protocols for lattice structures in robotic applications will be crucial for advancing the field. By adhering to best practices in design, manufacturing, and testing, engineers can effectively optimize lattice structures for FDM-printed swarm robotic nodes, leading to more efficient, robust, and capable swarm systems.

#### **Works cited**

1. Swarm Robotics: A Perspective on the Latest Reviewed Concepts and Applications \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC8000604/](https://pmc.ncbi.nlm.nih.gov/articles/PMC8000604/)  
2. Enhanced flexural strength-to-weight ratio of Inconel718 lattice structure parts made of powder bed fusion process \- Vamshi Veeraiahgari, Srinivasa Prakash Regalla, Suresh Kurra, 2024, accessed March 27, 2025, [https://journals.sagepub.com/doi/10.1177/09544062231198781?int.sj-full-text.similar-articles.3=](https://journals.sagepub.com/doi/10.1177/09544062231198781?int.sj-full-text.similar-articles.3)  
3. (PDF) An integrated Design Methodology for Swarm Robotics using Model-Based Systems Engineering and Robot Operating System \- ResearchGate, accessed March 27, 2025, [https://www.researchgate.net/publication/376820440\_An\_integrated\_Design\_Methodology\_for\_Swarm\_Robotics\_using\_Model-Based\_Systems\_Engineering\_and\_Robot\_Operating\_System](https://www.researchgate.net/publication/376820440_An_integrated_Design_Methodology_for_Swarm_Robotics_using_Model-Based_Systems_Engineering_and_Robot_Operating_System)  
4. Weight Reduction-Lattice Structure \- Pune \- 3D Incredible, accessed March 27, 2025, [https://3dincredible.com/engineering/weight-reduction-lattice-structure/](https://3dincredible.com/engineering/weight-reduction-lattice-structure/)  
5. A review of the types and tessellation of lattice structures, their effectiveness and limitations in mimicking natural cellular, accessed March 27, 2025, [https://www.matec-conferences.org/articles/matecconf/pdf/2023/15/matecconf\_rapdasa2023\_06008.pdf](https://www.matec-conferences.org/articles/matecconf/pdf/2023/15/matecconf_rapdasa2023_06008.pdf)  
6. What is a Lattice Structure? \- Optimise your 3D Printed Parts \- Additive-X, accessed March 27, 2025, [https://additive-x.com/blog/what-is-a-lattice-structure-optimise-your-3d-printed-parts](https://additive-x.com/blog/what-is-a-lattice-structure-optimise-your-3d-printed-parts)  
7. Understanding lattice structure for 3D Printing \- Swaay, accessed March 27, 2025, [https://swaay.com/understanding-lattice-structure-for-3d-printing](https://swaay.com/understanding-lattice-structure-for-3d-printing)  
8. Understanding 3D Printed Lattices \- SyBridge Technologies, accessed March 27, 2025, [https://sybridge.com/understanding-3d-printed-lattices-performance-and-design-considerations/](https://sybridge.com/understanding-3d-printed-lattices-performance-and-design-considerations/)  
9. Understanding Lattice Structure for 3D Printing \- Xometry, accessed March 27, 2025, [https://www.xometry.com/resources/3d-printing/lattice-structure-3d-printing/](https://www.xometry.com/resources/3d-printing/lattice-structure-3d-printing/)  
10. How 3D Printed Lattice Structures Improve Mechanical Properties, accessed March 27, 2025, [https://3dprinting.com/tips-tricks/3d-printed-lattice-structures/](https://3dprinting.com/tips-tricks/3d-printed-lattice-structures/)  
11. The Impact of 3D Printed Lattice Structures \- Manufacturing Today, accessed March 27, 2025, [https://manufacturing-today.com/news/the-impact-of-3d-printed-lattice-structures/](https://manufacturing-today.com/news/the-impact-of-3d-printed-lattice-structures/)  
12. Bio-inspired 3D-printed lattice structures for energy absorption applications: A review \- Doodi Ramakrishna, Gunji Bala Murali, 2023 \- Sage Journals, accessed March 27, 2025, [https://journals.sagepub.com/doi/10.1177/14644207221121948?icid=int.sj-abstract.citing-articles.3](https://journals.sagepub.com/doi/10.1177/14644207221121948?icid=int.sj-abstract.citing-articles.3)  
13. The Pros and Cons of Lattice Structures for 3D Printing (Part 1 \- Introduction) \- YouTube, accessed March 27, 2025, [https://www.youtube.com/watch?v=iRVt6mi2ES8](https://www.youtube.com/watch?v=iRVt6mi2ES8)  
14. Generative Design of Lattice Structures for 3D Printed Sculptures, accessed March 27, 2025, [https://generativeart.com/papersGA2022/2022/D%20Prete,%20S%20Picozzi%20ebook2022-18.pdf](https://generativeart.com/papersGA2022/2022/D%20Prete,%20S%20Picozzi%20ebook2022-18.pdf)  
15. What are lattice structures in additive manufacturing? \- nTop, accessed March 27, 2025, [https://www.ntop.com/resources/blog/guide-to-lattice-structures-in-additive-manufacturing/](https://www.ntop.com/resources/blog/guide-to-lattice-structures-in-additive-manufacturing/)  
16. Choosing the Right Lattice Structure in 3D Printing \- 3Dnatives, accessed March 27, 2025, [https://www.3dnatives.com/en/choosing-lattice-structure-in-3d-printing-251120244/](https://www.3dnatives.com/en/choosing-lattice-structure-in-3d-printing-251120244/)  
17. sybridge.com, accessed March 27, 2025, [https://sybridge.com/3d-lattice-design-elements/](https://sybridge.com/3d-lattice-design-elements/)  
18. 3D Printing Lattice Structures – The Ultimate Guide | All3DP Pro, accessed March 27, 2025, [https://all3dp.com/1/3d-printing-lattice-structures-the-ultimate-guide/](https://all3dp.com/1/3d-printing-lattice-structures-the-ultimate-guide/)  
19. Improved Mechanical Performance in FDM Cellular Frame Structures through Partial Incorporation of Faces, accessed March 27, 2025, [https://ntrs.nasa.gov/api/citations/20240011026/downloads/2024\_DSouza\_Polymers\_FDM\_Lattice.pdf](https://ntrs.nasa.gov/api/citations/20240011026/downloads/2024_DSouza_Polymers_FDM_Lattice.pdf)  
20. Design and Optimization of Lattice Structures: A Review \- MDPI, accessed March 27, 2025, [https://www.mdpi.com/2076-3417/10/18/6374](https://www.mdpi.com/2076-3417/10/18/6374)  
21. Strength to weight ratio for the lattice shell with different geometrical parameters., accessed March 27, 2025, [https://www.researchgate.net/figure/Strength-to-weight-ratio-for-the-lattice-shell-with-different-geometrical-parameters\_tbl4\_301313109](https://www.researchgate.net/figure/Strength-to-weight-ratio-for-the-lattice-shell-with-different-geometrical-parameters_tbl4_301313109)  
22. Mechanical Properties of Lattice Structures with a Central Cube: Experiments and Simulations \- MDPI, accessed March 27, 2025, [https://www.mdpi.com/1996-1944/17/6/1329](https://www.mdpi.com/1996-1944/17/6/1329)  
23. Mechanical Properties and Energy Absorption Characteristics of Additively Manufactured Lightweight Novel Re-Entrant Plate-Based Lattice Structures \- MDPI, accessed March 27, 2025, [https://www.mdpi.com/2073-4360/13/22/3882](https://www.mdpi.com/2073-4360/13/22/3882)  
24. Strength-to-weight ratio's of different unit lattices considered \- ResearchGate, accessed March 27, 2025, [https://www.researchgate.net/figure/Strength-to-weight-ratios-of-different-unit-lattices-considered\_tbl3\_384791240](https://www.researchgate.net/figure/Strength-to-weight-ratios-of-different-unit-lattices-considered_tbl3_384791240)  
25. Uniform and graded bio-inspired gyroid lattice: Effects of post-curing and print orientation on mechanical property \- Ganesh Chouhan, Gunji Bala Murali, 2024 \- Sage Journals, accessed March 27, 2025, [https://journals.sagepub.com/doi/10.1177/14644207231200729](https://journals.sagepub.com/doi/10.1177/14644207231200729)  
26. Topological and Mechanical Properties of Different Lattice Structures Based on Additive Manufacturing \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC9324303/](https://pmc.ncbi.nlm.nih.gov/articles/PMC9324303/)  
27. Mechanical properties and energy-absorption capabilities of thermoplastic sheet gyroid structures \- Zaguan, accessed March 27, 2025, [https://zaguan.unizar.es/record/110785/files/texto\_completo.pdf](https://zaguan.unizar.es/record/110785/files/texto_completo.pdf)  
28. Assessing Tetrahedral Lattice Parameters for Engineering Applications Through Finite Element Analysis \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC9828614/](https://pmc.ncbi.nlm.nih.gov/articles/PMC9828614/)  
29. Design, Simulation, and Mechanical Testing of 3D-Printed Titanium Lattice Structures, accessed March 27, 2025, [https://www.mdpi.com/2504-477X/7/1/32](https://www.mdpi.com/2504-477X/7/1/32)  
30. Mechanics of 3D-Printed Polymer Lattices with Varied Design and Processing Strategies, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC9788352/](https://pmc.ncbi.nlm.nih.gov/articles/PMC9788352/)  
31. Mechanical properties and fluid permeability of gyroid and diamond lattice structures for intervertebral devices: functional requirements and comparative analysis \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC8079052/](https://pmc.ncbi.nlm.nih.gov/articles/PMC8079052/)  
32. Mechanical properties of the gyroid as a function of relative density \- ResearchGate, accessed March 27, 2025, [https://www.researchgate.net/figure/Mechanical-properties-of-the-gyroid-as-a-function-of-relative-density-a-youngs\_fig2\_365556377](https://www.researchgate.net/figure/Mechanical-properties-of-the-gyroid-as-a-function-of-relative-density-a-youngs_fig2_365556377)  
33. SLA or FDM Printing for lattice structures? \- Grasshopper \- McNeel Forum, accessed March 27, 2025, [https://discourse.mcneel.com/t/sla-or-fdm-printing-for-lattice-structures/90338](https://discourse.mcneel.com/t/sla-or-fdm-printing-for-lattice-structures/90338)  
34. Enhancement over Conventional 3D Printed Lattice Structures and Investigation of their Mechanical Properties Using FEM Modeling, accessed March 27, 2025, [https://www.ajchem-a.com/article\_209067\_6eee9100ee751b0ba7b581c0ff3c369b.pdf](https://www.ajchem-a.com/article_209067_6eee9100ee751b0ba7b581c0ff3c369b.pdf)  
35. The Benefits of Using Lattice Structures in 3D Printing \- Protolabs, accessed March 27, 2025, [https://www.protolabs.com/resources/design-tips/using-lattice-structures-in-3d-printing-for-strength-and-lightweighting/](https://www.protolabs.com/resources/design-tips/using-lattice-structures-in-3d-printing-for-strength-and-lightweighting/)  
36. Self-supporting structure design for additive manufacturing by using a logistic aggregate function | Request PDF \- ResearchGate, accessed March 27, 2025, [https://www.researchgate.net/publication/332276450\_Self-supporting\_structure\_design\_for\_additive\_manufacturing\_by\_using\_a\_logistic\_aggregate\_function](https://www.researchgate.net/publication/332276450_Self-supporting_structure_design_for_additive_manufacturing_by_using_a_logistic_aggregate_function)  
37. Supportless Lattice Structure for Additive Manufacturing of Functional Products and the Evaluation of Its Mechanical Property at Variable Strain Rates, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC9697238/](https://pmc.ncbi.nlm.nih.gov/articles/PMC9697238/)  
38. FDM Layering Deposition Effects on Mechanical Response of TPU Lattice Structures \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC8510261/](https://pmc.ncbi.nlm.nih.gov/articles/PMC8510261/)  
39. Researchers 3D print support-free lattices inspired by sea urchins, accessed March 27, 2025, [https://3dprintingindustry.com/news/researchers-3d-print-support-free-lattices-inspired-by-sea-urchins-184606/](https://3dprintingindustry.com/news/researchers-3d-print-support-free-lattices-inspired-by-sea-urchins-184606/)  
40. Design of self-supporting lattices for additive manufacturing | Request PDF \- ResearchGate, accessed March 27, 2025, [https://www.researchgate.net/publication/348325067\_Design\_of\_self-supporting\_lattices\_for\_additive\_manufacturing](https://www.researchgate.net/publication/348325067_Design_of_self-supporting_lattices_for_additive_manufacturing)  
41. 3D Printed Mechanical Test Frame and Analysis of Lattice Structures – Duke MEMS, accessed March 27, 2025, [https://sites.duke.edu/memscapstone/katies-sandbox/](https://sites.duke.edu/memscapstone/katies-sandbox/)  
42. Influence of Geometric and Manufacturing Parameters on the Compressive Behavior of 3D Printed Polymer Lattice Structures, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC8002545/](https://pmc.ncbi.nlm.nih.gov/articles/PMC8002545/)  
43. Saving Weight with Metallic Lattice Structures: Design Challenges with a Real-World Example, accessed March 27, 2025, [https://repositories.lib.utexas.edu/bitstreams/66829db6-684a-4e27-8ca5-1b91c692d5f1/download](https://repositories.lib.utexas.edu/bitstreams/66829db6-684a-4e27-8ca5-1b91c692d5f1/download)  
44. Top Topology Optimization Software Solutions for 3D Printing \- 3Dnatives, accessed March 27, 2025, [https://www.3dnatives.com/en/topological-optimization-software-for-3d-printing-230920214/](https://www.3dnatives.com/en/topological-optimization-software-for-3d-printing-230920214/)  
45. Next-gen topology optimization software | nTop, accessed March 27, 2025, [https://www.ntop.com/software/capabilities/topology-optimization/](https://www.ntop.com/software/capabilities/topology-optimization/)  
46. The most advanced lattice generation software | nTop, accessed March 27, 2025, [https://www.ntop.com/software/capabilities/lattice-structures/](https://www.ntop.com/software/capabilities/lattice-structures/)  
47. Materialise 3-matic Industrial | 3D Data Optimization Software, accessed March 27, 2025, [https://www.materialise.com/en/industrial/software/3-matic](https://www.materialise.com/en/industrial/software/3-matic)  
48. Altair Sulis \- 3D Printing Generative Design Software, accessed March 27, 2025, [https://altair.com/sulis](https://altair.com/sulis)  
49. An Algorithm for Generating 3D Lattice Structures Suitable for Printing on a Multi-Plane FDM Printing Platform, accessed March 27, 2025, [http://larochelle.sdsmt.edu/publications/2015-2019/An%20Algorithm%20for%20Generating%203D%20Lattice%20Structures%20Suitable%20for%20Printing%20on%20a%20Multi-Plane%20FDM%20Printing%20Platform.pdf](http://larochelle.sdsmt.edu/publications/2015-2019/An%20Algorithm%20for%20Generating%203D%20Lattice%20Structures%20Suitable%20for%20Printing%20on%20a%20Multi-Plane%20FDM%20Printing%20Platform.pdf)  
50. An Algorithm for Generating 3D Lattice Structures Suitable for Printing on a Multi-Plane FDM Printing Platform | Request PDF \- ResearchGate, accessed March 27, 2025, [https://www.researchgate.net/publication/328711398\_An\_Algorithm\_for\_Generating\_3D\_Lattice\_Structures\_Suitable\_for\_Printing\_on\_a\_Multi-Plane\_FDM\_Printing\_Platform](https://www.researchgate.net/publication/328711398_An_Algorithm_for_Generating_3D_Lattice_Structures_Suitable_for_Printing_on_a_Multi-Plane_FDM_Printing_Platform)  
51. 3D Printing Threads and Adding Threaded Inserts to 3D Printed Parts (With Video), accessed March 27, 2025, [https://formlabs.com/blog/adding-screw-threads-3d-printed-parts/](https://formlabs.com/blog/adding-screw-threads-3d-printed-parts/)  
52. Embedding Nuts in 3D Printed Parts for Hidden Fastener Strength \- Markforged, accessed March 27, 2025, [https://markforged.com/resources/blog/embedding-nuts-3d-printing](https://markforged.com/resources/blog/embedding-nuts-3d-printing)  
53. Connect 3D Printed Parts | Design for Mass Production 3D Printing \- YouTube, accessed March 27, 2025, [https://www.youtube.com/watch?v=djm5tCFn9S0](https://www.youtube.com/watch?v=djm5tCFn9S0)  
54. connecting two 3d prints together : r/Fusion360 \- Reddit, accessed March 27, 2025, [https://www.reddit.com/r/Fusion360/comments/10d8x8q/connecting\_two\_3d\_prints\_together/](https://www.reddit.com/r/Fusion360/comments/10d8x8q/connecting_two_3d_prints_together/)  
55. Cables, Wires & Connectors \- RobotShop, accessed March 27, 2025, [https://www.robotshop.com/collections/cables-wires-connectors-en](https://www.robotshop.com/collections/cables-wires-connectors-en)  
56. What are 3D-Printed Connectors?, accessed March 27, 2025, [https://connectorsupplier.com/what-are-3d-printed-connectors/](https://connectorsupplier.com/what-are-3d-printed-connectors/)  
57. 8095 Robot Side Electrical Connector for QC75 \- EMI Corp, accessed March 27, 2025, [https://www.emicorp.com/item/CMAQC/8095-Robot-Side-Electrical-Connector-for-QC75/](https://www.emicorp.com/item/CMAQC/8095-Robot-Side-Electrical-Connector-for-QC75/)  
58. 3D Printer Electrical Connectors Guide \- YouTube, accessed March 27, 2025, [https://www.youtube.com/watch?v=y6G\_MhQFv3k](https://www.youtube.com/watch?v=y6G_MhQFv3k)  
59. 3D PRINT Integrating Circuits \- YouTube, accessed March 27, 2025, [https://www.youtube.com/watch?v=tk-iXMwzppQ](https://www.youtube.com/watch?v=tk-iXMwzppQ)  
60. How to Integrate Wires Into 3D-Prints to Build a 18650 Battery Charger \- Instructables, accessed March 27, 2025, [https://www.instructables.com/How-to-Integrate-Wires-Into-3D-Prints-to-Build-a-1/](https://www.instructables.com/How-to-Integrate-Wires-Into-3D-Prints-to-Build-a-1/)  
61. 3D Printed Integrated Sensors: From Fabrication to Applications—A Review \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC10745374/](https://pmc.ncbi.nlm.nih.gov/articles/PMC10745374/)  
62. Integrating Stereolithography and Direct Print Technologies for 3D Structural Electronics Fabrication \- ResearchGate, accessed March 27, 2025, [https://www.researchgate.net/publication/242336385\_Integrating\_Stereolithography\_and\_Direct\_Print\_Technologies\_for\_3D\_Structural\_Electronics\_Fabrication](https://www.researchgate.net/publication/242336385_Integrating_Stereolithography_and_Direct_Print_Technologies_for_3D_Structural_Electronics_Fabrication)  
63. Smart Lattice Structures with Self-Sensing Functionalities via Hybrid Additive Manufacturing Technology \- MDPI, accessed March 27, 2025, [https://www.mdpi.com/2072-666X/15/1/2](https://www.mdpi.com/2072-666X/15/1/2)  
64. Electrokinetic Properties of 3D-Printed Conductive Lattice Structures \- MDPI, accessed March 27, 2025, [https://www.mdpi.com/2076-3417/9/3/541](https://www.mdpi.com/2076-3417/9/3/541)  
65. The lattice structure design enables topology optimization of a robotic arm \- IRJET, accessed March 27, 2025, [https://www.irjet.net/archives/V10/i6/IRJET-V10I692.pdf](https://www.irjet.net/archives/V10/i6/IRJET-V10I692.pdf)  
66. 3D Printable Gradient Lattice Design for Multi-Stiffness Robotic Fingers \- arXiv, accessed March 27, 2025, [https://arxiv.org/pdf/2501.03763](https://arxiv.org/pdf/2501.03763)  
67. Modeling and Design of Lattice-Reinforced Pneumatic Soft Robots | Request PDF, accessed March 27, 2025, [https://www.researchgate.net/publication/375787573\_Modeling\_and\_Design\_of\_Lattice-Reinforced\_Pneumatic\_Soft\_Robots](https://www.researchgate.net/publication/375787573_Modeling_and_Design_of_Lattice-Reinforced_Pneumatic_Soft_Robots)  
68. Topological and lattice-based AM optimization for improving the structural efficiency of robotic arms \- Frontiers, accessed March 27, 2025, [https://www.frontiersin.org/journals/mechanical-engineering/articles/10.3389/fmech.2024.1422539/full](https://www.frontiersin.org/journals/mechanical-engineering/articles/10.3389/fmech.2024.1422539/full)  
69. What is a Lattice Structure in 3D Printing, accessed March 27, 2025, [https://www.3ds.com/make/solutions/blog/what-lattice-structure-3d-printing](https://www.3ds.com/make/solutions/blog/what-lattice-structure-3d-printing)  
70. Swarm topologies | Swarm Intelligence and Robotics Class Notes \- Fiveable, accessed March 27, 2025, [https://library.fiveable.me/swarm-intelligence-and-robotics/unit-4/swarm-topologies/study-guide/gFAgEFEtZwfa1uUl](https://library.fiveable.me/swarm-intelligence-and-robotics/unit-4/swarm-topologies/study-guide/gFAgEFEtZwfa1uUl)  
71. Usage of Evolutionary Algorithms in Swarm Robotics and Design Problems \- MDPI, accessed March 27, 2025, [https://www.mdpi.com/1424-8220/22/12/4437](https://www.mdpi.com/1424-8220/22/12/4437)  
72. Design and mechanical testing of 3d printed hierarchical lattices using biocompatible stereolithography \- TTU DSpace Repository, accessed March 27, 2025, [https://ttu-ir.tdl.org/items/1abf4f4f-cee5-4ba9-af10-6b4d7f976dee](https://ttu-ir.tdl.org/items/1abf4f4f-cee5-4ba9-af10-6b4d7f976dee)  
73. Cubic crystal system \- Wikipedia, accessed March 27, 2025, [https://en.wikipedia.org/wiki/Cubic\_crystal\_system](https://en.wikipedia.org/wiki/Cubic_crystal_system)  
74. Full article: Investigation on compression properties of perforated face-centered-cubic plate-lattice structures \- Taylor & Francis Online, accessed March 27, 2025, [https://www.tandfonline.com/doi/full/10.1080/15376494.2024.2447055?ai=198\&mi=8e4eys\&af=R](https://www.tandfonline.com/doi/full/10.1080/15376494.2024.2447055?ai=198&mi=8e4eys&af=R)  
75. Experimental and Simulation Analysis on the Mechanical Behavior of 3D‐Enhanced Al‐Based Tetrahedral Lattice Materials \- ResearchGate, accessed March 27, 2025, [https://www.researchgate.net/publication/366124844\_Experimental\_and\_Simulation\_Analysis\_on\_the\_Mechanical\_Behavior\_of\_3D-Enhanced\_Al-Based\_Tetrahedral\_Lattice\_Materials](https://www.researchgate.net/publication/366124844_Experimental_and_Simulation_Analysis_on_the_Mechanical_Behavior_of_3D-Enhanced_Al-Based_Tetrahedral_Lattice_Materials)  
76. A Study on Effective Compressive and Shear Properties of Tetrahedral Lattice Materials, accessed March 27, 2025, [https://www.worldscientific.com/doi/abs/10.1142/S1758825123500199](https://www.worldscientific.com/doi/abs/10.1142/S1758825123500199)  
77. Assessing Tetrahedral Lattice Parameters for Engineering Applications Through Finite Element Analysis \- PubMed, accessed March 27, 2025, [https://pubmed.ncbi.nlm.nih.gov/36654835/](https://pubmed.ncbi.nlm.nih.gov/36654835/)