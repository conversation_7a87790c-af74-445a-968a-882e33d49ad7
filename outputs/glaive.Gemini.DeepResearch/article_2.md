# **Implications of Fixed Ratio Versus Variable Ratio Schedules in Token Economy Systems for Individuals with Developmental Disabilities**

Token economy systems represent a structured behavioral intervention strategy designed to foster the acquisition of adaptive behaviors and the reduction of problem behaviors, particularly for individuals with developmental disabilities 1. These systems operate by awarding tangible tokens immediately following the demonstration of specific desired behaviors. These tokens then serve as generalized conditioned reinforcers, which can be accumulated and subsequently exchanged for a variety of reinforcing items or privileges, known as backup reinforcers 1. The effectiveness of a token economy lies in its ability to provide clear, visual feedback regarding behavioral expectations and achievements, thereby strengthening the connection between behavior and desired outcomes 2. Rooted in the principles of operant conditioning, specifically positive reinforcement, token economies can be tailored to meet the unique needs and preferences of individuals with diverse developmental profiles 1. The selection of an appropriate reinforcement schedule is a critical element in the design of any token economy, as it dictates the frequency with which reinforcement is delivered and significantly influences the patterns of responding 5. Among the various schedules of reinforcement, fixed ratio (FR) and variable ratio (VR) schedules are frequently considered, each possessing distinct characteristics that can differentially impact motivation, consistency, resistance to extinction, and engagement 5. This report aims to provide a comprehensive analysis comparing the implications of employing FR versus VR schedules within token economy systems for individuals with developmental disabilities, with a specific focus on their effects on promoting adaptive behavior and reducing problem behavior.

A fixed ratio schedule of reinforcement is characterized by the delivery of reinforcement after a predictable and unchanging number of responses 8. In practical terms within a token economy, this means an individual will receive a token after completing a specific number of target behaviors, such as finishing a set number of math problems or engaging in a designated duration of on-task behavior 6. This schedule is denoted as "FR" followed by the number of required responses; for example, FR5 indicates that reinforcement is provided after every five correct responses 8. The inherent predictability of an FR schedule can be both advantageous and disadvantageous. On one hand, knowing the exact number of responses required for reinforcement can provide a clear and understandable goal, potentially leading to high initial rates of responding 4. Individuals may be motivated to complete the required work to earn the anticipated token. However, this predictability also often results in a brief pause in responding immediately following the delivery of the reinforcer, known as the post-reinforcement pause 4. Furthermore, if the number of required responses (the ratio) is set too high, it can lead to fatigue, frustration, and even burnout, potentially diminishing the quality of work produced 4. While FR schedules can be effective for the initial acquisition of new skills due to their clear contingency, they tend to be more susceptible to extinction compared to variable schedules once reinforcement is no longer provided 7. Real-world examples of FR schedules include piecework in factories where workers are paid for every fixed number of items produced, or commission-based sales where a bonus is earned after a specific number of sales 4. These examples highlight the utility of FR schedules in situations where a high quantity of output is desired and the behavior can be easily counted 12.

In contrast to the fixed nature of FR schedules, a variable ratio schedule of reinforcement involves providing reinforcement after an unpredictable number of responses, with the reinforcement being delivered on an average 9. In a token economy employing a VR schedule, an individual might receive a token after varying numbers of target behaviors; for instance, a token might be awarded after 2 correct responses, then after 5, then after 3, and so on, with the average number of responses needed for reinforcement being predetermined but not explicitly known to the individual 5. This schedule is written as "VR" followed by the average number of responses, such as VR3 5. The defining characteristic of VR schedules is their unpredictability, which leads to distinct behavioral patterns. One of the most notable effects is the maintenance of high and steady rates of responding 4. Because individuals do not know exactly when the next reinforcement will occur, they tend to respond consistently in the hope that the current response will be the one that earns them a token 13. Additionally, behaviors reinforced on a VR schedule are highly resistant to extinction 5. Even when reinforcement is no longer provided, the behavior tends to persist for a longer duration because the individual has learned that reinforcement occurs unpredictably and may still be forthcoming 15. VR schedules also typically result in only a brief pause, if any, after reinforcement 14. Due to their unpredictable nature and the persistent responding they generate, VR schedules are often likened to gambling, particularly slot machines, where reinforcement occurs after a variable number of lever pulls 10. This type of schedule is frequently used to promote the generalization and maintenance of learned behaviors 10. The unpredictability inherent in VR schedules can also increase motivation and engagement, as individuals remain focused on the task, anticipating the possibility of reinforcement 9.

Token economy systems are widely implemented for individuals with developmental disabilities to foster the development of adaptive behaviors across various domains, including self-care, communication, social interaction, and academic skills 1. The fundamental principle involves clearly defining target behaviors that are meaningful and achievable for the individual 2. Upon demonstrating these behaviors, the individual earns tokens, which serve as a visual representation of their progress 18. Clear expectations regarding which behaviors earn tokens are crucial for the individual to understand what is required of them, fostering a structured and predictable environment 2. The accumulated tokens can then be exchanged for a predetermined menu of backup reinforcers that hold value for the individual, such as preferred toys, activities, or privileges 1. This system provides immediate feedback, reinforcing the connection between the adaptive behavior and the desired outcome, thereby increasing the likelihood of the behavior being repeated in the future 1. The visual aspect of a token economy, where individuals can see their tokens accumulate, plays a significant role in enhancing motivation and engagement 3. Moreover, token economies can be adapted to gradually increase the complexity of tasks as the individual progresses, supporting the development of self-management and task completion skills 3. Research has consistently shown that token economies can significantly increase adaptive behaviors in children with autism and ADHD, creating a structured environment that supports skill development 3.

Beyond promoting adaptive behaviors, token economy systems are also effectively employed to reduce problem behaviors in individuals with developmental disabilities 21. One common approach involves awarding tokens for the absence of specific problem behaviors within defined time intervals or upon completion of tasks 22. This strategy, known as differential reinforcement of other behavior (DRO), reinforces not engaging in the problem behavior 24. Additionally, token economies can incorporate response cost, where tokens are removed following the occurrence of a problem behavior 22. This creates a direct consequence for engaging in unwanted actions, thereby reducing their frequency. Furthermore, the system can be used to reinforce alternative behaviors that are incompatible with the problem behavior (DRI), providing the individual with a positive and appropriate way to earn reinforcement instead of engaging in the problem behavior 24. Studies conducted in various settings, including classrooms and psychiatric units, have demonstrated the effectiveness of token economies in reducing disruptive behaviors and promoting a more functional environment 22. The key to success lies in clearly defining the problem behaviors, consistently implementing the token system, and ensuring that the reinforcement for the absence of problem behavior or for engaging in alternative behaviors is more appealing than the reinforcement maintaining the problem behavior 25.

The implementation of a fixed ratio schedule within a token economy system for individuals with developmental disabilities has notable implications for motivation. Initially, the predictability of receiving a token after a specific number of desired responses can be highly motivating 4. The individual understands precisely what is required to earn reinforcement, which can provide a clear sense of accomplishment upon reaching the target number of responses 6. For instance, a child might be motivated to complete a set of three chores to earn a star token. However, this motivation can fluctuate. A common characteristic of FR schedules is the post-reinforcement pause, where the individual may exhibit a temporary decrease in motivation immediately after receiving a token 4. This pause suggests that once the immediate goal of earning the token is achieved, the drive to engage in further adaptive behaviors may temporarily subside. Moreover, if the fixed ratio is set too high, requiring a large number of responses for a single token, it can lead to decreased motivation, frustration, and potential burnout, especially for individuals who may have limited attention spans or require more frequent reinforcement 4. It is also important to consider that using extrinsic rewards like tokens on an FR schedule for behaviors that an individual is already intrinsically motivated to perform might inadvertently reduce their inherent desire to engage in those behaviors in the long run 20.

In terms of promoting consistency of adaptive behaviors, fixed ratio schedules can lead to high rates of responding until the predetermined number of responses is completed 4. The individual works diligently to reach the required ratio to obtain the token. However, the post-reinforcement pause introduces an element of inconsistency. Following the receipt of a token, there might be a period of reduced engagement in the target behavior until the motivation to earn another token builds up again 4. This can result in a pattern of bursts of the adaptive behavior followed by periods of relative inactivity 4. Furthermore, consistency can be negatively impacted if the individual experiences fatigue or a decrease in motivation due to a high response requirement 4. The predictable nature of the FR schedule might also lead to a "work then break" mentality, where the individual engages in the required number of responses to earn the token and then takes a break before initiating further adaptive behaviors 26. This pattern might not be conducive to maintaining a continuous and consistent engagement in adaptive behaviors over extended periods.

Conversely, the use of a variable ratio schedule in a token economy system for individuals with developmental disabilities has distinct effects on motivation. The unpredictability of reinforcement inherent in VR schedules typically leads to high and sustained levels of motivation 9. Because the individual does not know after how many responses they will receive a token, they are motivated to continue engaging in the desired behaviors consistently 5. This uncertainty creates a sense of anticipation, as each response holds the potential for reinforcement 16. Unlike FR schedules, VR schedules are less prone to satiation, where the individual becomes less responsive to the reinforcer due to overexposure, because the reinforcement is delivered intermittently and unpredictably 17. For individuals with autism, VR schedules have been found to increase persistence and engagement in desired behaviors 13. The unpredictable nature can also introduce an element of novelty, making the learning process more engaging and maintaining the individual's interest over time 13.

One of the most significant advantages of employing a variable ratio schedule in a token economy is the high resistance to extinction of the adaptive behaviors it promotes 5. Once a behavior has been reinforced on a VR schedule, it is more likely to persist even when the delivery of tokens becomes less frequent or even stops entirely 15. This is because the individual has learned that reinforcement occurs unpredictably and the absence of a token after a particular response does not necessarily signal that reinforcement will no longer be available 17. The variability in the number of responses required for reinforcement makes the behavior less likely to diminish when reinforcement is withheld, as the individual may continue to respond in the hope of eventually receiving a token 13. This characteristic is particularly valuable for promoting the long-term maintenance of adaptive behaviors in individuals with developmental disabilities, even in settings where the token economy might not always be in effect 13.

Furthermore, variable ratio schedules have a strong potential for maintaining engagement in adaptive behaviors. They typically lead to a high and steady rate of responding without significant post-reinforcement pauses 4. The unpredictability of when a token will be earned keeps individuals focused and engaged in the task at hand 13. For individuals with autism, the introduction of unpredictability through a VR schedule can also help reduce repetitive behaviors and increase engagement in social interactions, communication, and academic tasks 13. The element of surprise and the sustained motivation to earn the next unpredictable reinforcement contribute to a more consistent and enjoyable learning experience, fostering better participation and retention of adaptive behaviors 13.

When comparing the effectiveness of fixed ratio and variable ratio schedules in promoting adaptive behavior within token economy systems for individuals with developmental disabilities, several key differences emerge. FR schedules can be effective for the initial acquisition of new skills due to their predictability, providing a clear understanding of the response-reinforcement contingency 10. The fixed number of responses required offers structure and can lead to high response rates for the specified duration 6. However, the presence of post-reinforcement pauses and the potential for burnout, especially with higher ratios, can hinder sustained engagement in adaptive behaviors 4. In contrast, VR schedules are generally more effective for maintaining adaptive behaviors once they have been learned 5. They promote high and steady rates of responding, stronger motivation for continuous engagement, and greater resistance to extinction 5. The unpredictability of VR schedules can be particularly beneficial for individuals with autism in maintaining interest and motivation 13. Research has also indicated that VR schedules can lead to better outcomes in terms of attention and task completion compared to FR schedules 28. A dynamic approach, starting with the clarity of an FR schedule for initial learning and transitioning to the sustained motivation and resistance to extinction offered by a VR schedule for maintenance, could be an optimal strategy for promoting adaptive behaviors in token economy systems 7.

In the context of reducing problem behavior, both FR and VR schedules can be utilized within a token economy. An FR schedule can be employed to reinforce the absence of a problem behavior for a fixed number of intervals or upon completion of a set number of tasks. The predictability might aid in the individual's understanding of the contingency 24. However, FR schedules might be less effective in suppressing problem behaviors that are themselves maintained by unpredictable reinforcement in the natural environment, and the post-reinforcement pause could see a return of unwanted behaviors. On the other hand, VR schedules can be used to reinforce the absence of problem behavior on an unpredictable basis, potentially mirroring real-world scenarios more closely 24. This unpredictability can make it more challenging for individuals to anticipate when reinforcement will occur for the problem behavior, potentially leading to a reduction in its frequency 24. When VR schedules are used to reinforce behaviors that are incompatible with the problem behavior, they can be particularly effective in competing with and reducing the occurrence of unwanted actions 24. Research has shown that VR schedules can lead to a decrease in disruptive behavior compared to FR schedules 28. While FR schedules offer a clear contingency, the unpredictable nature and greater resistance to extinction of VR schedules might make them more effective for the long-term suppression and generalization of reductions in problem behavior 7. Reinforcing positive opposite behaviors on a variable schedule can be a powerful indirect strategy for minimizing problem behaviors.

In conclusion, both fixed ratio and variable ratio schedules of reinforcement have distinct implications when implemented within token economy systems for individuals with developmental disabilities. FR schedules offer predictability that can be beneficial for initial skill acquisition and providing clear expectations, often leading to high rates of responding until reinforcement is delivered. However, they are also associated with post-reinforcement pauses and a potential for decreased motivation or burnout, and are more susceptible to extinction. Conversely, VR schedules excel at maintaining motivation, promoting consistent engagement, and increasing resistance to extinction for adaptive behaviors due to their unpredictable nature. VR schedules also show promise in reducing problem behaviors, potentially because their unpredictability mirrors real-world contingencies more closely and contributes to a greater resistance to extinction. The choice between these schedules should be guided by the specific goals of the intervention, whether it is the initial acquisition of a new behavior or the long-term maintenance and generalization of an existing one, as well as the individual's unique needs and learning style.

Based on the analysis, the following recommendations are offered for practitioners:

* **Initiate with Continuous Reinforcement:** When introducing a new adaptive behavior, begin with continuous reinforcement (CRF) to establish a strong association between the behavior and the token 6.  
* **Utilize Fixed Ratio for Skill Building:** Once the behavior is reliably occurring, transition to a low fixed ratio (e.g., FR1 or FR2) to build fluency, gradually increasing the ratio as mastery is demonstrated 10.  
* **Employ Variable Ratio for Maintenance and Generalization:** For the long-term maintenance of adaptive behaviors and to promote generalization across settings, shift to a low average variable ratio schedule (e.g., VR3), gradually increasing the ratio to thin reinforcement 5.  
* **Consider Variable Ratio for Problem Behavior Reduction:** When addressing problem behaviors, consider using a variable ratio schedule to reinforce the absence of the behavior or the occurrence of an incompatible behavior 22.  
* **Individualize Reinforcement Schedules:** Tailor the specific ratio values in both FR and VR schedules to the individual's abilities, motivation levels, and the complexity of the target behavior, regularly assessing and adjusting the schedule as needed 3.  
* **Ensure Meaningful Backup Reinforcers:** Utilize highly motivating backup reinforcers to maintain engagement with the token economy system, regardless of the reinforcement schedule employed 2.  
* **Monitor Progress and Collect Data:** Continuously monitor the individual's responding under each schedule to track progress and identify any potential issues 2.  
* **Plan for Gradual Fading:** As adaptive behaviors become consistent, develop a plan for gradually fading the token economy system to foster intrinsic motivation and independence 13.

Further research could explore the optimal strategies for transitioning between different reinforcement schedules within token economy systems and the effectiveness of various VR ratios for specific adaptive and problem behaviors in individuals with diverse developmental disabilities 30.

| Feature | Fixed Ratio (FR) | Variable Ratio (VR) | Implications for Token Economy with Developmental Disabilities |
| :---- | :---- | :---- | :---- |
| **Reinforcement Delivery** | After a fixed number of responses | After an unpredictable number of responses (on average) | FR provides predictability for initial learning; VR sustains motivation and persistence |
| **Response Rate** | High and steady until reinforcement, followed by a pause | High and steady with minimal pauses | VR promotes more consistent engagement in adaptive behaviors |
| **Resistance to Extinction** | Lower compared to VR | Higher | VR is more effective for long-term maintenance of adaptive behaviors |
| **Motivation** | High initial motivation, potential for decrease after reinforcement and burnout | High and sustained motivation | VR can be more effective in maintaining long-term motivation |
| **Engagement** | Can lead to a "work then break" pattern | Consistent engagement | VR fosters continuous participation and can reduce repetitive behaviors |
| **Use for Skill Acquisition** | Effective for initial learning due to predictability | Less suitable for initial acquisition | FR provides clearer contingencies for new skill development |
| **Use for Problem Behavior Reduction** | Can reinforce absence of behavior for a fixed duration | Can reinforce absence of behavior unpredictably or incompatible behaviors | VR's unpredictability may be more effective for long-term suppression |

#### **Works cited**

1. How Are Token Economy Systems Applied in ABA Therapy?, accessed March 25, 2025, [https://www.magnetaba.com/blog/aba-therapy-for-token-economy-systems](https://www.magnetaba.com/blog/aba-therapy-for-token-economy-systems)  
2. ABA Therapy Strategies for Token Economy Systems, accessed March 25, 2025, [https://www.apexaba.com/blog/aba-therapy-for-token-economy-systems?6b55a564\_page=6](https://www.apexaba.com/blog/aba-therapy-for-token-economy-systems?6b55a564_page=6)  
3. ABA Therapy for Token Economy Systems, accessed March 25, 2025, [https://www.discoveryaba.com/aba-therapy/aba-therapy-for-token-economy-systems](https://www.discoveryaba.com/aba-therapy/aba-therapy-for-token-economy-systems)  
4. What Is a Fixed-Ratio Schedule of Reinforcement? \- Verywell Mind, accessed March 25, 2025, [https://www.verywellmind.com/what-is-a-fixed-ratio-schedule-2795190](https://www.verywellmind.com/what-is-a-fixed-ratio-schedule-2795190)  
5. Schedules of Reinforcement, accessed March 25, 2025, [https://www3.uca.edu/iqzoo/Learning%20Principles/lammers/All\_schedules.htm](https://www3.uca.edu/iqzoo/Learning%20Principles/lammers/All_schedules.htm)  
6. Schedules of Reinforcement \- ABA Therapist Jobs, accessed March 25, 2025, [https://www.abatherapistjobs.com/applied-behaviour-analysis/schedules-of-reinforcement](https://www.abatherapistjobs.com/applied-behaviour-analysis/schedules-of-reinforcement)  
7. How Schedules of Reinforcement Work in Psychology \- Verywell Mind, accessed March 25, 2025, [https://www.verywellmind.com/what-is-a-schedule-of-reinforcement-2794864](https://www.verywellmind.com/what-is-a-schedule-of-reinforcement-2794864)  
8. learningbehavioranalysis.com, accessed March 25, 2025, [https://learningbehavioranalysis.com/b-5-schedules/\#:\~:text=Fixed%20Ratio%20(FR),your%20textbook%20(FR%205).](https://learningbehavioranalysis.com/b-5-schedules/#:~:text=Fixed%20Ratio%20\(FR\),your%20textbook%20\(FR%205\).)  
9. B-5: Define and provide examples of schedules of reinforcement, accessed March 25, 2025, [https://learningbehavioranalysis.com/b-5-schedules/](https://learningbehavioranalysis.com/b-5-schedules/)  
10. Mastering Reinforcement Schedules: Fixed, Variable, Ratio, and Interval Explained, accessed March 25, 2025, [https://btexamreview.com/basic-reinforcement-schedules-guide-rbt/](https://btexamreview.com/basic-reinforcement-schedules-guide-rbt/)  
11. www.verywellmind.com, accessed March 25, 2025, [https://www.verywellmind.com/what-is-a-fixed-ratio-schedule-2795190\#:\~:text=In%20operant%20conditioning%2C%20a%20fixed,the%20delivery%20of%20the%20reinforcer.](https://www.verywellmind.com/what-is-a-fixed-ratio-schedule-2795190#:~:text=In%20operant%20conditioning%2C%20a%20fixed,the%20delivery%20of%20the%20reinforcer.)  
12. Reinforcement Schedules – General Psychology \- UCF Pressbooks \- University of Central Florida, accessed March 25, 2025, [https://pressbooks.online.ucf.edu/lumenpsychology/chapter/reading-reinforcement-schedules/](https://pressbooks.online.ucf.edu/lumenpsychology/chapter/reading-reinforcement-schedules/)  
13. Variable Ratio Schedule & Examples Uncovered \- Brighter Strides ABA, accessed March 25, 2025, [https://www.brighterstridesaba.com/blog/variable-ratio-schedule-and-examples](https://www.brighterstridesaba.com/blog/variable-ratio-schedule-and-examples)  
14. Variable-Ratio Schedule Characteristics and Examples, accessed March 25, 2025, [https://www.verywellmind.com/what-is-a-variable-ratio-schedule-2796012](https://www.verywellmind.com/what-is-a-variable-ratio-schedule-2796012)  
15. Reinforcement Schedules | Introduction to Psychology \- Lumen Learning, accessed March 25, 2025, [https://courses.lumenlearning.com/waymaker-psychology/chapter/reading-reinforcement-schedules/](https://courses.lumenlearning.com/waymaker-psychology/chapter/reading-reinforcement-schedules/)  
16. Variable-Ratio Schedule Characteristics and Examples | Above and Beyond Therapy, accessed March 25, 2025, [https://www.abtaba.com/blog/variable-ratio-schedule](https://www.abtaba.com/blog/variable-ratio-schedule)  
17. Variable Ratio Schedule & Examples \- Apex ABA Therapy, accessed March 25, 2025, [https://www.apexaba.com/blog/variable-ratio-schedule-examples](https://www.apexaba.com/blog/variable-ratio-schedule-examples)  
18. How to Use Token Systems to Teach New Skills to Children with Autism, accessed March 25, 2025, [https://www.mastermindbehavior.com/post/how-to-use-token-systems-to-teach-new-skills-to-children-with-autism](https://www.mastermindbehavior.com/post/how-to-use-token-systems-to-teach-new-skills-to-children-with-autism)  
19. Token Economy: Examples and Applications in ABA, accessed March 25, 2025, [https://masteraba.com/token-economy-2/](https://masteraba.com/token-economy-2/)  
20. Token Economy ABA – Systems & Uses in Therapy \- CentralReach, accessed March 25, 2025, [https://centralreach.com/blog/token-economy-aba/](https://centralreach.com/blog/token-economy-aba/)  
21. The Science of the Token Economy System \- Brighter Strides ABA, accessed March 25, 2025, [https://www.brighterstridesaba.com/blog/token-economy](https://www.brighterstridesaba.com/blog/token-economy)  
22. Token Economy | EBSCO Research Starters, accessed March 25, 2025, [https://www.ebsco.com/research-starters/psychology/token-economy](https://www.ebsco.com/research-starters/psychology/token-economy)  
23. The Use of a Token Economy to Decrease Problem Behaviors and Increase Task Completion for a Pre-Schooler with Autism, accessed March 25, 2025, [https://digitalcommons.lindenwood.edu/cgi/viewcontent.cgi?article=2006\&context=theses](https://digitalcommons.lindenwood.edu/cgi/viewcontent.cgi?article=2006&context=theses)  
24. Using a Token Economy and Differential Reinforcement in SPED \- Chalkboard Superhero, accessed March 25, 2025, [https://chalkboardsuperhero.com/2023/09/using-a-token-economy-and-differential-reinforcement-in-sped/](https://chalkboardsuperhero.com/2023/09/using-a-token-economy-and-differential-reinforcement-in-sped/)  
25. (PDF) Guidelines for Establishing and Maintaining Token Economies \- ResearchGate, accessed March 25, 2025, [https://www.researchgate.net/publication/288988265\_Guidelines\_for\_Establishing\_and\_Maintaining\_Token\_Economies](https://www.researchgate.net/publication/288988265_Guidelines_for_Establishing_and_Maintaining_Token_Economies)  
26. The Importance of Reinforcement Schedules in Behavior Therapy ..., accessed March 25, 2025, [https://www.mastermindbehavior.com/post/the-importance-of-reinforcement-schedules-in-behavior-therapy-programs](https://www.mastermindbehavior.com/post/the-importance-of-reinforcement-schedules-in-behavior-therapy-programs)  
27. Examples of Variable Ratio Schedules Uncovered \- Mastermind Behavior Services, accessed March 25, 2025, [https://www.mastermindbehavior.com/post/variable-ratio-schedule-and-examples](https://www.mastermindbehavior.com/post/variable-ratio-schedule-and-examples)  
28. A comparison of the effects of fixed and variable ratio schedules of reinforcement on the behavior of deaf children \- PMC, accessed March 25, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC1308102/](https://pmc.ncbi.nlm.nih.gov/articles/PMC1308102/)  
29. A Comparison of the Effects of Fixed and Variable Ratio Schedules of Reinforcement on the Behavior of Deaf Children. \- SciSpace, accessed March 25, 2025, [https://scispace.com/pdf/a-comparison-of-the-effects-of-fixed-and-variable-ratio-v5c9ndcde2.pdf](https://scispace.com/pdf/a-comparison-of-the-effects-of-fixed-and-variable-ratio-v5c9ndcde2.pdf)  
30. full abstract \- B. F. Skinner Foundation, accessed March 25, 2025, [https://www.bfskinner.org/wp-content/uploads/2014/08/Catalina-Rey-Research-B.-F.-Skinner-Foundation-FABA-Research-Award.docx](https://www.bfskinner.org/wp-content/uploads/2014/08/Catalina-Rey-Research-B.-F.-Skinner-Foundation-FABA-Research-Award.docx)