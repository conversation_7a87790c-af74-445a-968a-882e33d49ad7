# **Modeling Epigenetic Regulation of Gene Expression as a Noisy Communication Channel with Sparse Coding and Compressive Sensing**

Gene expression, the process by which the information encoded in DNA is used to synthesize functional gene products, is a cornerstone of cellular biology. This intricate process is not solely dictated by the underlying genetic sequence but is also profoundly influenced by epigenetic modifications. These heritable alterations, which do not involve changes to the DNA sequence, play a pivotal role in determining when, where, and to what extent genes are expressed 1. Primarily encompassing histone modifications and DNA methylation, epigenetic mechanisms regulate gene expression by modulating the structure of chromatin and its accessibility to the transcriptional machinery 1. Given the complexity inherent in these regulatory processes and the vast amount of data generated by modern epigenomic techniques, there is a pressing need for sophisticated modeling approaches. This report proposes a conceptual framework that draws an analogy between epigenetic regulation and a noisy communication channel, wherein epigenetic marks serve as the encoded message, and the binding of transcription factors and RNA polymerase represents the decoding process. Furthermore, it explores the potential of applying advanced signal processing techniques, specifically sparse coding and compressive sensing, to identify the most informative epigenetic marks within this framework, aiming to simplify the understanding of these complex regulatory networks. The application of information theory to biological systems allows for a quantitative perspective on the flow of regulatory information, acknowledging the inherent stochasticity at the molecular level 6. Similarly, sparse coding and compressive sensing offer powerful mathematical tools to dissect the high-dimensional epigenetic landscape and pinpoint the key signals relevant to gene expression 8.

## **Epigenetic Modifications: The Encoding Mechanism**

The packaging of eukaryotic DNA into chromatin, a highly organized structure, is fundamental to the regulation of gene expression. The basic unit of chromatin is the nucleosome, which consists of DNA wrapped around a core of histone proteins 1. The N-terminal tails of these histone proteins extend from the nucleosome and are subject to a diverse array of post-translational modifications (PTMs) 1. These modifications, including acetylation, methylation, phosphorylation, ubiquitination, sumoylation, and ADP-ribosylation, can directly alter the physical properties of chromatin, thereby influencing the interaction between histones and DNA 16. Certain modifications can disrupt these interactions, leading to a more relaxed and accessible chromatin state known as euchromatin, which is generally permissive for gene transcription 1. Conversely, other modifications can strengthen histone-DNA interactions, resulting in a more condensed and inaccessible state called heterochromatin, which is typically associated with gene silencing 1.

For instance, the addition of an acetyl group to lysine residues on histone tails, a process often catalyzed by histone acetyltransferases (HATs), neutralizes the positive charge of lysine, reducing its electrostatic attraction to the negatively charged DNA 1. This relaxation of chromatin structure enhances the accessibility of DNA to transcription factors and RNA polymerase, thus promoting gene transcription 11. Conversely, histone deacetylases (HDACs) remove these acetyl groups, leading to a more compact chromatin structure and transcriptional repression 11. Histone methylation, carried out by histone methyltransferases (HMTs) and reversed by histone demethylases (KDMs), does not alter the charge of histones but can have diverse effects on gene expression depending on the specific lysine (K) or arginine (R) residue that is methylated and the degree of methylation 1. For example, trimethylation of lysine 4 on histone H3 (H3K4me3) is generally associated with transcriptional activation, while trimethylation of lysine 9 (H3K9me3) and lysine 27 (H3K27me3) on histone H3 are often linked to gene silencing 11. Histone phosphorylation, regulated by kinases and phosphatases, plays a crucial role in processes such as chromosome condensation during cell division, DNA repair, and transcriptional regulation 11. For instance, phosphorylation of histone H3 at serine 10 (H3S10ph) is involved in chromatin compaction during mitosis and has been associated with the expression of certain proto-oncogenes 11.

The sheer variety of histone modifications and their site-specific effects on chromatin structure and the recruitment of regulatory proteins suggest a highly complex and finely tuned encoding system capable of specifying a wide range of transcriptional states. Each modification can act as a distinct signal, and the location and type of modification can have different consequences for gene expression. This combinatorial nature points towards a sophisticated regulatory language. Furthermore, the enzymatic control of histone modifications by "writers" that add modifications and "erasers" that remove them 11 underscores the dynamic and reversible nature of this epigenetic layer. This allows for rapid adaptation of gene expression patterns in response to cellular signals and environmental changes.

DNA methylation is another pivotal epigenetic modification involving the covalent addition of a methyl group to the 5th carbon of a cytosine base, predominantly occurring in the context of CpG dinucleotides 1. In mammals, DNA methylation is generally associated with the repression of gene transcription, especially when it occurs within the promoter regions of genes 1. This repression can be mediated by physically hindering the binding of transcriptional proteins to the DNA or, more significantly, by the recruitment of methyl-CpG-binding domain (MBD) proteins, which in turn recruit other proteins such as histone deacetylases, leading to chromatin compaction and gene silencing 1. DNA methylation plays crucial roles in fundamental biological processes such as genomic imprinting, X-chromosome inactivation in females, and the repression of transposable elements to maintain genome stability 1. The establishment and maintenance of DNA methylation patterns are tightly controlled by a family of enzymes called DNA methyltransferases (DNMTs). DNMT3A and DNMT3B are primarily responsible for establishing new (de novo) methylation patterns during development, while DNMT1 acts as a maintenance methyltransferase, copying existing methylation patterns to newly synthesized DNA strands during replication 33. The removal of methyl groups (demethylation) is an equally important process, mediated by enzymes such as the ten-eleven translocation (TET) family of dioxygenases 33. Notably, DNA methylation can also occur within the gene body of actively transcribed genes, where it may play a role in regulating splicing and suppressing the activity of cryptic promoters 21.

DNA methylation provides a more stable and heritable epigenetic mark compared to many histone modifications 1, suggesting its critical role in establishing and maintaining long-term gene silencing and cellular identity across cell divisions. The maintenance mechanism ensures that once a methylation pattern is established, it can be faithfully copied to daughter cells, contributing to the stable inheritance of epigenetic states. Aberrations in DNA methylation patterns, such as hypermethylation of tumor suppressor genes or hypomethylation leading to oncogene activation, are frequently observed in various diseases, particularly cancer 3, underscoring the profound impact of this epigenetic mark on human health. Disruptions in the normal DNA methylation landscape can lead to dysregulation of critical genes involved in cell growth, differentiation, and other essential processes.

The concept of the "histone code" proposes that specific combinations and sequential patterns of different histone modifications, acting in concert, create a complex language that dictates chromatin structure and ultimately gene expression 13. For instance, the co-occurrence of both a repressive mark like H3K27me3 and an active mark like H3K4me3 at certain genomic regions in embryonic stem cells defines "bivalent domains," which are thought to poise genes for activation during differentiation 12. Furthermore, histone modifications and DNA methylation do not function in isolation but rather engage in extensive cross-talk, influencing each other's establishment, maintenance, and function 1. For example, DNA methylation can recruit histone-modifying enzymes, and certain histone modifications can guide DNA methylation patterns 17. Specific histone modification patterns are associated with distinct genomic features and transcriptional states, such as active promoters (e.g., H3K4me3, H3K27ac), enhancers (e.g., H3K4me1, H3K27ac), gene bodies (e.g., H3K36me3), and repressed regions (e.g., H3K9me3, H3K27me3) 12. This intricate interplay and combinatorial nature of histone modifications and DNA methylation create a highly sophisticated and context-dependent epigenetic encoding system that allows for precise and nuanced regulation of gene expression in response to a wide range of cellular and environmental cues. The observed cross-talk between different epigenetic modifications implies a hierarchical and coordinated system of regulation, where the presence or absence of one mark can influence the deposition or removal of others, leading to the establishment and maintenance of specific chromatin states and gene expression profiles.

| Histone Modification | Location | General Effect on Gene Expression | Enzymes Involved (Writers/Erasers) |
| :---- | :---- | :---- | :---- |
| H3K4me3 | Promoters | Activation | SET1 complex; KDM5 family |
| H3K27ac | Promoters, Enhancers | Activation | CBP/p300; HDACs |
| H3K9me3 | Heterochromatin | Repression | Suv39H1; KDMs (e.g., JMJD2 family) |
| H3K27me3 | Gene-rich regions | Repression | PRC2 complex (EZH2); KDM6 family |
| H3K36me3 | Gene bodies | Activation | SETD2; KDMs (e.g., KDM4 family) |
| H3S10ph | Mitotic chromosomes | Chromosome condensation, gene expression | Kinases (e.g., Aurora B); Phosphatases (e.g., PP1) |
| H4K20me1 | Gene bodies | Activation | PR-Set7/SET8; KDMs (e.g., JMJD6) |
| H3K9ac | Promoters, Enhancers | Activation | GCN5, PCAF; HDACs |
| H4K16ac | Repetitive sequences | Activation | MOF; HDACs |
| H2BK120ub1 | Gene bodies | Activation | RNF20/RNF40; Deubiquitinases (e.g., USP22) |

## **Gene Expression as a Noisy Communication Channel**

Information theory, a mathematical framework developed by Claude Shannon, provides the tools to quantify information and analyze its transmission through communication systems, especially in the presence of noise 43. A central concept is the noisy communication channel, which models the transmission of a message from a sender to a receiver via a medium that can introduce distortions or errors, collectively termed noise 6. In this model, a source generates a message, which is encoded by a transmitter into a signal for transmission through the channel. Noise can corrupt the signal, and the receiver decodes the received signal to reconstruct the original message. Key metrics include channel capacity, representing the maximum rate of reliable information transmission, and mutual information, quantifying the shared information between the input and output, considering noise 44. Applying this framework to biological systems, particularly gene regulation, allows for a mathematical perspective on the inherent uncertainty and variability, enabling the quantification of the efficiency and reliability of information transfer from regulatory signals to gene expression outcomes 45.

Mapping the biological components of gene regulation onto the elements of this communication channel provides a valuable conceptual tool. The initial signal or information that needs to be conveyed through gene regulation, such as a cellular state or an environmental cue, can be considered the **source** 44. The **encoder** in this analogy is represented by the epigenetic modifications, specifically the patterns of histone modifications and DNA methylation established at the regulatory regions of target genes. These modifications act as a code that dictates DNA accessibility and the likelihood of transcription initiation 1. The complex chromatin environment, including its three-dimensional organization and associated proteins, constitutes the **channel**. This channel can introduce **noise** through the inherent stochasticity of molecular interactions within the cell, such as the random binding and unbinding of transcription factors, RNA polymerase, and epigenetic modifying enzymes, as well as fluctuations in molecular concentrations and environmental perturbations 6. The **decoder** is the binding of transcription factors and RNA polymerase to specific DNA sequences, a process significantly influenced by the epigenetic landscape 50. Finally, the **receiver** is the resulting level of gene expression, measured as mRNA transcript abundance or protein concentration, which represents the cell's response to the initial signal but is a noisy version of the intended message 1. This mapping allows for the analysis of information flow from regulatory signals through epigenetic encoding, the noisy chromatin environment, and transcriptional decoding to the final gene expression output, enabling the consideration of potential information loss or distortion at each step. Recognizing gene regulation as a noisy communication channel emphasizes the importance of robustness and error correction mechanisms within the biological system. The epigenetic code and the decoding machinery must be sufficiently resilient to ensure reliable gene expression despite the inherent stochasticity and potential for noise to interfere with the process.

| Information Theory Component | Biological Counterpart |
| :---- | :---- |
| Source | Cellular state/environmental signals |
| Encoder | Histone modifications and DNA methylation patterns |
| Channel | Chromatin environment and cellular machinery |
| Noise | Stochasticity in molecular interactions and environmental fluctuations |
| Decoder | Binding of transcription factors and RNA polymerase |
| Receiver | Gene expression levels (mRNA abundance, protein concentration) |

## **The Decoding Process: Transcription Factor and RNA Polymerase Binding in the Context of Epigenetics**

Transcription factors (TFs), sequence-specific DNA-binding proteins, are central to the regulation of gene expression 50. They recognize and bind to specific DNA sequences, often located in the promoter and enhancer regions of genes. RNA polymerase, the enzyme responsible for transcribing DNA into RNA, binds to the promoter region of a gene and initiates the synthesis of an RNA molecule complementary to the DNA template 50. The precise binding of TFs and RNA polymerase to DNA is a critical step in controlling when, where, and at what level genes are expressed 50. This binding can be influenced by various factors, including the presence of other proteins (co-activators or co-repressors) and the local chromatin environment. The binding of transcription factors and RNA polymerase to DNA represents the crucial decoding stage in our analogy, where the information encoded in the DNA sequence and the superimposed epigenetic landscape is interpreted to initiate the process of gene transcription. The outcome of this binding event directly determines whether and how much a gene will be transcribed.

Epigenetic modifications, particularly histone modifications and DNA methylation, significantly influence the accessibility of DNA to transcription factors and RNA polymerase by modulating the structure and compaction of chromatin 1. Regions of open chromatin, often characterized by activating histone modifications such as acetylation, provide greater access for TFs and RNA polymerase to bind to their target DNA sequences, thus facilitating gene transcription 1. Conversely, regions of condensed chromatin, often associated with repressive histone modifications and DNA methylation, hinder the binding of these proteins, leading to transcriptional repression 1. For example, DNA methylation in promoter regions can directly impede TF binding or recruit MBD proteins that further compact chromatin 1. Interestingly, transcription factors themselves can also influence the epigenetic landscape by recruiting histone-modifying enzymes to their target sites, creating a feedback loop that can either enhance or repress gene expression 53. RNA polymerase III transcription is also regulated by epigenetic mechanisms, with compact chromatin generally being repressive, and histone modifications indicative of open or closed chromatin states influencing its activity 54. Epigenetic modifications thus act as a critical layer of control that directly influences the ability of the transcriptional machinery to access and interpret the genetic code, effectively gating the decoding process. The dynamic interplay between transcription factors and epigenetic modifiers, where TFs can recruit modifying enzymes and the resulting modifications can affect TF binding, suggests a complex and self-regulating decoding mechanism that allows for fine-tuning of gene expression in response to various signals.

Chromatin structure is not static but rather a dynamic entity that can be actively remodeled by specialized protein complexes (chromatin remodelers) 4. These complexes can alter the positioning and organization of nucleosomes along the DNA, affecting the accessibility of underlying DNA sequences. Chromatin remodeling is often guided by epigenetic modifications, which can serve as docking sites for these complexes 11. For instance, certain histone modifications can recruit specific remodeling complexes that either open or condense the chromatin structure. This dynamic remodeling of chromatin is essential for allowing transcription factors and RNA polymerase to access their binding sites on the DNA, especially in regions that are otherwise tightly packaged within nucleosomes 4. It facilitates the assembly of transcriptional complexes on gene promoters and enhancers. Chromatin remodeling thus provides the essential dynamic component to the decoding process, allowing for rapid and precise changes in gene expression in response to various intracellular and extracellular signals. It ensures that the transcriptional machinery can gain access to the genome when and where needed, overcoming the inherent barrier posed by chromatin packaging.

## **Identifying Informative Epigenetic Marks using Sparse Coding**

Sparse coding is an unsupervised machine learning technique aimed at representing high-dimensional data as a linear combination of a small number of basis elements or dictionary atoms 55. The primary objective is to achieve a representation where most coefficients are zero or close to zero, effectively capturing the data's underlying structure with a minimal set of active components. Unlike Principal Component Analysis (PCA), which focuses on capturing maximum variance through a lower-dimensional representation, sparse coding seeks an over-complete basis where the number of basis elements exceeds the data's dimensionality, allowing for a more nuanced capture of complex patterns 57. Sparsity is typically enforced by adding an L1 regularization penalty to the reconstruction error in the objective function, encouraging coefficients to be exactly zero 56. The concept of sparse coding has been inspired by findings in neuroscience, suggesting that sensory information in the brain might be encoded using sparse representations, where only a small fraction of neurons are active at any given time 55. Sparse coding offers a powerful approach to address the challenge of high dimensionality in epigenetic data by identifying a minimal set of key epigenetic marks that are most informative for predicting or explaining gene expression. By finding a sparse representation, we can effectively perform feature selection and focus on the most relevant regulatory signals.

Sparse coding has been successfully applied in various areas of biological data analysis, including the representation and classification of protein sequences 8, the analysis of single-cell RNA sequencing data to identify gene modules and their relationship to phenotypes 61, and feature selection in genomics for cancer prognosis 10. In these applications, sparse coding has demonstrated its ability to capture intricate relationships within the data, outperform traditional methods, and provide more interpretable models by highlighting the most important features. For instance, in the context of T cell receptor (TCR) protein sequences, sparse coding has been used to extract key features for multi-classifying sequences with cancer categories, achieving high accuracy 8. Similarly, in single-cell RNA-seq analysis, sparse representation learning has been used to model cellular variation and reveal the effects of biological conditions on gene expression 61. The demonstrated success of sparse coding in other high-dimensional biological data domains strongly suggests its potential utility for analyzing epigenetic data and identifying the key epigenetic marks that drive gene expression. Given its effectiveness in related areas like genomics and transcriptomics, sparse coding is a promising candidate for tackling the complexity of epigenetic regulation. The underlying principles of identifying a minimal set of informative features align well with the goal of understanding which epigenetic marks are most crucial for gene expression.

One potential strategy for applying sparse coding to identify the most influential epigenetic marks on gene expression involves representing epigenetic data (e.g., levels of various histone modifications and DNA methylation at different genomic locations) as input vectors for a sparse coding model. The corresponding gene expression levels for these samples could be used as a target variable in a supervised or semi-supervised sparse coding framework. The sparse coding algorithm would then learn a dictionary of basis elements, where each basis element potentially represents a pattern or combination of epigenetic marks. The coefficients associated with each basis element for a given sample would indicate the extent to which that pattern is present and its contribution to the observed gene expression level. Epigenetic marks or combinations of marks that consistently have non-zero coefficients across different samples and are strongly correlated with gene expression would be identified as the most informative. This could involve analyzing the learned basis elements and their associated coefficients to determine which specific epigenetic features are most predictive of gene expression outcomes. Another approach could involve using sparse coding for dimensionality reduction of the epigenetic data first, and then using the sparse representation as features in a downstream model to predict gene expression. The features with the highest weights in the sparse representation would be considered the most influential epigenetic marks. By applying sparse coding techniques to the paired epigenetic and gene expression data, we can potentially uncover hidden relationships and identify a reduced set of epigenetic features that are most predictive of gene expression, offering a more parsimonious and interpretable model of epigenetic regulation. While the provided research snippets do not offer extensive examples of the direct application of sparse coding for identifying key epigenetic marks influencing gene expression, they do highlight the use of sparse methods for feature selection in epigenetics and related fields 8. For instance, sparse modeling-based learning techniques have been used for feature selection in various classification problems 10. The increasing interest in integrating multi-omics data, including epigenetic and transcriptomic data, suggests that sparse coding could be a valuable tool in this area for identifying the most relevant features across different data types that contribute to gene regulation or disease phenotypes 64. Further research and development of specific sparse coding methodologies tailored to the unique characteristics of epigenetic data are likely to yield valuable insights into the epigenetic control of gene expression.

## **Efficient Data Acquisition and Analysis with Compressive Sensing**

Compressive sensing (CS), also known as compressive sampling or sparse sampling, is a signal processing technique that enables the efficient acquisition and reconstruction of signals that are sparse or compressible in some domain, using far fewer samples than required by the traditional Nyquist-Shannon sampling theorem 65. The fundamental principle behind CS is that if a signal has a sparse representation (i.e., most of its coefficients are zero or very small) in a particular basis, it can be accurately reconstructed from a limited number of non-adaptive linear measurements, provided that the measurement matrix is incoherent with the sparsity basis 65. Incoherence essentially means that the measurement process does not preferentially sample the components that are sparse. The number of measurements required for successful reconstruction is typically proportional to the sparsity level of the signal rather than its bandwidth, allowing for significant data reduction during acquisition. Reconstruction of the original signal from the under-sampled measurements is typically achieved by solving an optimization problem that seeks the sparsest signal that is consistent with the acquired measurements, often involving L1-norm minimization 56. Compressive sensing offers a potentially transformative approach to the acquisition of high-dimensional epigenetic data by allowing researchers to obtain a rich representation of the epigenetic landscape with significantly fewer measurements than traditional methods, leading to reduced experimental costs and time. Epigenomic studies often involve profiling numerous epigenetic marks across the entire genome, generating massive datasets. Compressive sensing could enable more efficient experimental designs by strategically sampling the epigenetic landscape.

Epigenetic data, such as genome-wide maps of histone modifications or DNA methylation profiles, often exhibit some degree of sparsity or compressibility. For example, large genomic regions might be uniformly methylated or lack a particular histone modification, leading to sparse representations in appropriate bases (e.g., wavelet bases, piecewise constant representations) 9. Compressive sensing could be applied to design more efficient experimental protocols for epigenomic studies. For instance, in ChIP-seq experiments, instead of sequencing to very high depth across the entire genome, a CS-based approach might involve targeting specific genomic regions or using multiplexing strategies that allow for the acquisition of compressed measurements, followed by computational reconstruction of the full epigenetic profile. Similarly, in DNA methylation studies using bisulfite sequencing, CS could potentially be used to reduce the number of CpG sites that need to be interrogated, especially if the methylation patterns exhibit regional coherence or sparsity in their differences across conditions. By exploiting the inherent sparsity or compressibility of epigenetic signals, compressive sensing could enable researchers to conduct large-scale epigenomic studies with significantly reduced sequencing costs and computational resources, making it feasible to investigate epigenetic regulation in a wider range of biological contexts and with greater statistical power.

One strategy for designing efficient experiments and analyses using compressive sensing to identify key epigenetic marks involves identifying a suitable basis in which the epigenetic data of interest (e.g., histone modification levels across the genome) is sparse or compressible. This might require prior knowledge about the expected patterns of these modifications. Next, a measurement matrix that is incoherent with this sparsity basis would need to be designed. This matrix would define how the epigenetic landscape is sampled. In practice, this could involve selecting a subset of genomic locations or epigenetic marks to measure in a specific way. The under-sampled epigenetic data obtained through this measurement process would then be reconstructed using appropriate CS reconstruction algorithms, which typically involve solving an optimization problem to find the sparsest signal consistent with the measurements. Once the full or a sufficiently accurate representation of the epigenetic landscape is reconstructed, sparse coding techniques could be applied to this data, along with corresponding gene expression data, to identify the most informative epigenetic marks or patterns associated with gene regulation. Combining compressive sensing for efficient data acquisition of the epigenetic landscape with sparse coding for subsequent feature selection of the reconstructed data could provide a powerful and cost-effective pipeline for identifying key epigenetic regulators of gene expression. The provided snippets indicate that compressive sensing has been applied in various biological contexts, including increasing the efficiency of spatial transcriptomics methods for inferring gene abundances 9 and studying the epigenetic effects of compressive forces on cells 71. Snippet 70 also proposes segmentation as a compression framework for epigenetic signals. These examples suggest that the principles of compressive sensing are being explored in the context of biological data, including data related to gene regulation and epigenetic modifications. However, there is a need for more research specifically focused on applying CS to the problem of identifying key epigenetic marks that influence gene expression. The ability of CS to reduce sampling requirements while preserving the ability to reconstruct sparse signals makes it a promising technique for future epigenomic studies aiming to uncover the regulatory code governing gene expression.

## **A Conceptual Framework for Modeling Epigenetic Regulation of Gene Expression as a Noisy Channel with Sparse Coding and Compressive Sensing**

Our proposed framework integrates the analogy of epigenetic regulation as a noisy communication channel with the powerful data analysis techniques of sparse coding and compressive sensing. In this framework, the epigenetic landscape (patterns of histone modifications and DNA methylation) serves as the encoded message that is transmitted through the noisy channel of the chromatin environment. The binding of transcription factors and RNA polymerase acts as the decoding mechanism, ultimately leading to a gene expression output. Sparse coding can be applied to the high-dimensional epigenetic data, potentially after efficient acquisition using compressive sensing, to identify the most informative epigenetic marks or patterns that are strongly associated with variations in gene expression. These informative marks represent the key components of the encoded message that are most effectively "read" by the decoding machinery. Compressive sensing can be strategically employed to reduce the experimental burden of acquiring comprehensive epigenetic data by focusing on measurements that allow for the accurate reconstruction of the sparse representation of the epigenetic landscape, particularly the informative marks identified through sparse coding.

The proposed model outlining the flow of information from epigenetic modifications to gene expression, incorporating noise and methods for identifying key signals, consists of the following steps:

1. **Encoding:** Cellular state or environmental signals trigger the establishment of specific epigenetic modification patterns (histone marks and DNA methylation) at gene regulatory regions. This pattern represents the encoded message.  
2. **Transmission through a Noisy Channel:** The chromatin environment and inherent stochasticity in molecular interactions introduce noise, potentially affecting the stability and interpretation of the epigenetic code.  
3. **Efficient Data Acquisition via Compressive Sensing:** Employ compressive sensing strategies to acquire under-sampled measurements of the epigenetic landscape, focusing on capturing information relevant to the sparse representation of informative marks.  
4. **Reconstruction of Sparse Epigenetic Signal:** Apply appropriate reconstruction algorithms to recover a sparse representation of the epigenetic landscape from the compressed measurements.  
5. **Decoding:** Transcription factors and RNA polymerase bind to DNA based on the reconstructed epigenetic landscape and the underlying DNA sequence, initiating transcription. This process is also subject to noise.  
6. **Gene Expression Output:** The resulting gene expression level is the decoded message, potentially influenced by noise at various stages.  
7. **Identification of Informative Marks using Sparse Coding:** Apply sparse coding to the reconstructed epigenetic data in relation to gene expression levels to identify the minimal set of epigenetic marks or patterns that are most predictive of gene expression outcomes, effectively filtering out noise and less relevant signals.

This framework provides a novel and integrated perspective on epigenetic regulation by combining the intuitive analogy of a noisy communication channel with the powerful data reduction and feature selection capabilities of sparse coding and the efficient data acquisition potential of compressive sensing. It offers a roadmap for identifying key regulatory epigenetic marks and for designing more efficient experimental strategies. However, the communication channel analogy is a simplification of a highly complex biological reality. The effectiveness of sparse coding and compressive sensing relies on the assumption of sparsity or compressibility in the epigenetic data, which may not always hold true. Experimental validation is crucial to confirm the functional significance of the informative marks identified by this framework. The optimal design of measurement matrices for compressive sensing in epigenomics and the development of tailored sparse coding algorithms for epigenetic data remain areas for further research. This integrated conceptual framework offers a potentially powerful approach to unravel the complexities of epigenetic regulation of gene expression by providing both a theoretical lens and a set of computational tools to identify the key players in this fundamental biological process.

## **Conclusion and Future Perspectives**

This report has presented a conceptual framework for understanding the intricate relationship between epigenetic modifications and gene expression by drawing an analogy to a noisy communication channel. In this model, histone modifications and DNA methylation serve as the encoding mechanism, the chromatin environment acts as a noisy channel, and the binding of transcription factors and RNA polymerase represents the decoding process leading to gene expression. The application of sparse coding and compressive sensing techniques has been proposed as a means to identify the most informative epigenetic marks within this framework, offering potential for dimensionality reduction and efficient data acquisition.

Future research should focus on developing and validating computational models based on this framework using comprehensive datasets of epigenetic modifications and gene expression across various cell types and conditions. Investigating the specific sources and characteristics of noise in the epigenetic communication channel and exploring how these can be explicitly modeled are also crucial next steps. Furthermore, applying this framework to understand epigenetic dysregulation in the context of various diseases, such as cancer and developmental disorders, could lead to the identification of potential therapeutic targets. Continued refinement and optimization of sparse coding and compressive sensing techniques specifically for the analysis of different types of epigenetic data, taking into account their unique characteristics, will be essential. Finally, exploring the dynamic aspects of epigenetic regulation within this framework, considering how changes in epigenetic marks over time influence gene expression, represents a promising avenue for future investigations.

| Enzyme Class | Specific Enzymes | Primary Function |
| :---- | :---- | :---- |
| DNA Methyltransferases (DNMTs) | DNMT1 | Maintenance of methylation patterns during DNA replication |
|  | DNMT3A | De novo methylation, establishing new methylation patterns during development |
|  | DNMT3B | De novo methylation, establishing new methylation patterns during development |
| Ten-Eleven Translocation (TET) Enzymes | TET1 | Oxidation of 5-methylcytosine (5mC) to 5-hydroxymethylcytosine (5hmC) |
|  | TET2 | Oxidation of 5-methylcytosine (5mC) to 5-hydroxymethylcytosine (5hmC) |
|  | TET3 | Oxidation of 5-methylcytosine (5mC) to 5-hydroxymethylcytosine (5hmC) |

#### **Works cited**

1. Epigenetic Modifications: Basic Mechanisms and Role in Cardiovascular Disease \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC3107542/](https://pmc.ncbi.nlm.nih.gov/articles/PMC3107542/)  
2. Epigenetic Regulation by Histone Methylation and Histone Variants \- Oxford Academic, accessed March 27, 2025, [https://academic.oup.com/mend/article/19/3/563/2741273](https://academic.oup.com/mend/article/19/3/563/2741273)  
3. How DNA methylation affects gene expression \- biomodal, accessed March 27, 2025, [https://biomodal.com/blog/how-dna-methylation-affects-gene-expression/](https://biomodal.com/blog/how-dna-methylation-affects-gene-expression/)  
4. Epigenetic Modifications in Genome Help Remembering the Stress Tolerance Strategy Adopted by the Plant \- IMR Press, accessed March 27, 2025, [https://www.imrpress.com/journal/FBL/29/3/10.31083/j.fbl2903126/htm](https://www.imrpress.com/journal/FBL/29/3/10.31083/j.fbl2903126/htm)  
5. What Are Histones? Understanding Their Role In Gene Expression \- Genemod, accessed March 27, 2025, [https://genemod.net/blog/what-are-histones-understanding-their-role-in-gene-expression](https://genemod.net/blog/what-are-histones-understanding-their-role-in-gene-expression)  
6. Determining the limitations and benefits of noise in gene regulation and signal transduction through single cell, microscopy-based analysis, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC5402475/](https://pmc.ncbi.nlm.nih.gov/articles/PMC5402475/)  
7. Interplay between gene expression noise and regulatory network architecture \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC3340541/](https://pmc.ncbi.nlm.nih.gov/articles/PMC3340541/)  
8. arXiv:2304.13145v2 \[cs.LG\] 5 Sep 2023 \- SciSpace, accessed March 27, 2025, [https://scispace.com/pdf/t-cell-receptor-protein-sequences-and-sparse-coding-a-novel-2ybuyy2b.pdf](https://scispace.com/pdf/t-cell-receptor-protein-sequences-and-sparse-coding-a-novel-2ybuyy2b.pdf)  
9. Compressed sensing for highly efficient imaging transcriptomics \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC8355028/](https://pmc.ncbi.nlm.nih.gov/articles/PMC8355028/)  
10. A Sparse-Modeling Based Approach for Class Specific Feature Selection \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC7924712/](https://pmc.ncbi.nlm.nih.gov/articles/PMC7924712/)  
11. Histone Modification \- News-Medical, accessed March 27, 2025, [https://www.news-medical.net/life-sciences/Histone-Modification.aspx](https://www.news-medical.net/life-sciences/Histone-Modification.aspx)  
12. The correlation between histone modifications and gene expression \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC4230708/](https://pmc.ncbi.nlm.nih.gov/articles/PMC4230708/)  
13. How histone modifications impact gene regulation | biomodal, accessed March 27, 2025, [https://biomodal.com/blog/how-histone-modifications-impact-gene-regulation/](https://biomodal.com/blog/how-histone-modifications-impact-gene-regulation/)  
14. Histone Modification Studies | EpigenTek, accessed March 27, 2025, [https://www.epigentek.com/catalog/histone-modification.php](https://www.epigentek.com/catalog/histone-modification.php)  
15. Unraveling the Dynamic World of Histone Modifications: Implications in Gene Regulation and Disease Pathogenesis \- Cusabio, accessed March 27, 2025, [https://www.cusabio.com/c-20829.html](https://www.cusabio.com/c-20829.html)  
16. Histone modifications | Abcam, accessed March 27, 2025, [https://www.abcam.com/en-us/technical-resources/guides/epigenetics-guide/histone-modifications](https://www.abcam.com/en-us/technical-resources/guides/epigenetics-guide/histone-modifications)  
17. Chromatin structure and histone modifications | Genomics Class Notes \- Fiveable, accessed March 27, 2025, [https://library.fiveable.me/genomics/unit-6/chromatin-structure-histone-modifications/study-guide/JYDdXeT9sEG8ezyN](https://library.fiveable.me/genomics/unit-6/chromatin-structure-histone-modifications/study-guide/JYDdXeT9sEG8ezyN)  
18. The interplay of histone modifications – writers that read \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC4641500/](https://pmc.ncbi.nlm.nih.gov/articles/PMC4641500/)  
19. biomodal.com, accessed March 27, 2025, [https://biomodal.com/blog/how-dna-methylation-affects-gene-expression/\#:\~:text=Methylation%20and%20acetylation%20significantly%20impact,relaxed%20chromatin%20that%20enhances%20transcription.](https://biomodal.com/blog/how-dna-methylation-affects-gene-expression/#:~:text=Methylation%20and%20acetylation%20significantly%20impact,relaxed%20chromatin%20that%20enhances%20transcription.)  
20. www.abcam.com, accessed March 27, 2025, [https://www.abcam.com/en-us/technical-resources/guides/epigenetics-guide/histone-modifications\#:\~:text=Histone%20acetylation%20is%20largely%20targeted,and%20promoters%20of%20active%20genes.](https://www.abcam.com/en-us/technical-resources/guides/epigenetics-guide/histone-modifications#:~:text=Histone%20acetylation%20is%20largely%20targeted,and%20promoters%20of%20active%20genes.)  
21. DNA methylation \- Wikipedia, accessed March 27, 2025, [https://en.wikipedia.org/wiki/DNA\_methylation](https://en.wikipedia.org/wiki/DNA_methylation)  
22. Epigenetics, Health, and Disease | Genomics and Your Health \- CDC, accessed March 27, 2025, [https://www.cdc.gov/genomics-and-health/epigenetics/index.html](https://www.cdc.gov/genomics-and-health/epigenetics/index.html)  
23. The impact of DNA methylation on gene regulation in placental development, accessed March 27, 2025, [https://www.trophoblast.cam.ac.uk/impact-dna-methylation-gene-regulation-placental-development](https://www.trophoblast.cam.ac.uk/impact-dna-methylation-gene-regulation-placental-development)  
24. Decoding the epigenome with Oxford Nanopore real-time methylation detection \- YouTube, accessed March 27, 2025, [https://www.youtube.com/watch?v=G2ybW5RdSuQ](https://www.youtube.com/watch?v=G2ybW5RdSuQ)  
25. Comparing feature selection and machine learning approaches for predicting CYP2D6 methylation from genetic variation \- Frontiers, accessed March 27, 2025, [https://www.frontiersin.org/journals/neuroinformatics/articles/10.3389/fninf.2023.1244336/full](https://www.frontiersin.org/journals/neuroinformatics/articles/10.3389/fninf.2023.1244336/full)  
26. Histone Modifying Enzymes \- Creative BioMart, accessed March 27, 2025, [https://www.creativebiomart.net/research-area-histone-modifying-enzymes-470.htm](https://www.creativebiomart.net/research-area-histone-modifying-enzymes-470.htm)  
27. Histone-modifying enzymes \- Wikipedia, accessed March 27, 2025, [https://en.wikipedia.org/wiki/Histone-modifying\_enzymes](https://en.wikipedia.org/wiki/Histone-modifying_enzymes)  
28. Histone-modifying enzymes: regulators of developmental decisions and drivers of human disease \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC3382990/](https://pmc.ncbi.nlm.nih.gov/articles/PMC3382990/)  
29. Full article: Histone-Modifying Enzymes: Regulators of Developmental Decisions and Drivers of Human Disease \- Taylor and Francis, accessed March 27, 2025, [https://www.tandfonline.com/doi/full/10.2217/epi.12.3](https://www.tandfonline.com/doi/full/10.2217/epi.12.3)  
30. The roles of histone modifications in tumorigenesis and associated inhibitors in cancer therapy \- PMC \- PubMed Central, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC11256729/](https://pmc.ncbi.nlm.nih.gov/articles/PMC11256729/)  
31. The interplay of histone modifications – writers that read | EMBO reports, accessed March 27, 2025, [https://www.embopress.org/doi/10.15252/embr.201540945](https://www.embopress.org/doi/10.15252/embr.201540945)  
32. The Mutagenic Consequences of DNA Methylation within and across Generations \- MDPI, accessed March 27, 2025, [https://www.mdpi.com/2075-4655/6/4/33](https://www.mdpi.com/2075-4655/6/4/33)  
33. What is DNA methylation? | DNA Methylation Overview \- biomodal, accessed March 27, 2025, [https://biomodal.com/blog/the-fascinating-world-of-dna-methylation/](https://biomodal.com/blog/the-fascinating-world-of-dna-methylation/)  
34. DNA Methylation and Demethylation Are Regulated by Functional DNA Methyltransferases and DnTET Enzymes in Diuraphis noxia \- Frontiers, accessed March 27, 2025, [https://www.frontiersin.org/journals/genetics/articles/10.3389/fgene.2020.00452/full](https://www.frontiersin.org/journals/genetics/articles/10.3389/fgene.2020.00452/full)  
35. DNA demethylation \- Wikipedia, accessed March 27, 2025, [https://en.wikipedia.org/wiki/DNA\_demethylation](https://en.wikipedia.org/wiki/DNA_demethylation)  
36. pmc.ncbi.nlm.nih.gov, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC3236603/\#:\~:text=DNMT3A%20and%203B%20are%20responsible,DNA%20demethylation%20via%20DNA%20repair.](https://pmc.ncbi.nlm.nih.gov/articles/PMC3236603/#:~:text=DNMT3A%20and%203B%20are%20responsible,DNA%20demethylation%20via%20DNA%20repair.)  
37. DNA Demethylation Dynamics \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC3236603/](https://pmc.ncbi.nlm.nih.gov/articles/PMC3236603/)  
38. DNA Methylation and Demethylation in Mammals \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC3099650/](https://pmc.ncbi.nlm.nih.gov/articles/PMC3099650/)  
39. digitalcommons.library.tmc.edu, accessed March 27, 2025, [https://digitalcommons.library.tmc.edu/utgsbs\_dissertations/1264/\#:\~:text=There%20are%20two%20classes%20of,to%20fully%20methylated%20CpGs%20during](https://digitalcommons.library.tmc.edu/utgsbs_dissertations/1264/#:~:text=There%20are%20two%20classes%20of,to%20fully%20methylated%20CpGs%20during)  
40. Establishing, maintaining and modifying DNA methylation patterns in plants and animals \- PMC \- PubMed Central, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC3034103/](https://pmc.ncbi.nlm.nih.gov/articles/PMC3034103/)  
41. Establishment and maintenance of DNA methylation patterns in mammals \- PubMed, accessed March 27, 2025, [https://pubmed.ncbi.nlm.nih.gov/16570848/](https://pubmed.ncbi.nlm.nih.gov/16570848/)  
42. Epigenetic interplay between histone modifications and DNA methylation in gene silencing \- PubMed, accessed March 27, 2025, [https://pubmed.ncbi.nlm.nih.gov/18407786/](https://pubmed.ncbi.nlm.nih.gov/18407786/)  
43. Full article: Information theory and the ethylene genetic network \- Taylor & Francis Online, accessed March 27, 2025, [https://www.tandfonline.com/doi/full/10.4161/psb.6.10.16424](https://www.tandfonline.com/doi/full/10.4161/psb.6.10.16424)  
44. The application of information theory to biochemical signaling systems \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC3820280/](https://pmc.ncbi.nlm.nih.gov/articles/PMC3820280/)  
45. CHANNEL CAPACITY IN NOISY BIOCHEMICAL SIGNALLING NETWORKS \- KKZMBM)\!, accessed March 27, 2025, [https://kkzmbm.mimuw.edu.pl/sprawozdania/spr20/cale/dziekanska.pdf](https://kkzmbm.mimuw.edu.pl/sprawozdania/spr20/cale/dziekanska.pdf)  
46. The positive role of noise for information acquisition in biological signaling pathways, accessed March 27, 2025, [https://www.biorxiv.org/content/10.1101/762989.full](https://www.biorxiv.org/content/10.1101/762989.full)  
47. The positive role of noise for information acquisition in biological signaling pathways, accessed March 27, 2025, [https://www.biorxiv.org/content/10.1101/762989v1.full-text](https://www.biorxiv.org/content/10.1101/762989v1.full-text)  
48. New Perspective on Gene Regulation Highlighted on BiophysJ Cover \- Biophysical Society, accessed March 27, 2025, [https://www.biophysics.org/blog/new-perspective-on-gene-regulation-highlighted-on-biophysj-cover](https://www.biophysics.org/blog/new-perspective-on-gene-regulation-highlighted-on-biophysj-cover)  
49. 15\. The noisy, noisy nature of gene expression: How stochastic fluctuations create variation, accessed March 27, 2025, [https://biocircuits.github.io/chapters/15\_noise.html](https://biocircuits.github.io/chapters/15_noise.html)  
50. Gene expression, transcription factors and epigenetics \- A Level Biology \- YouTube, accessed March 27, 2025, [https://www.youtube.com/watch?v=vdANObA4Dpg](https://www.youtube.com/watch?v=vdANObA4Dpg)  
51. Chapter 13: Transcriptional Control and Epigenetics \- Chemistry, accessed March 27, 2025, [https://wou.edu/chemistry/courses/online-chemistry-textbooks/ch450-and-ch451-biochemistry-defining-life-at-the-molecular-level/chapter-13-transcriptional-control-and-epigenetics/](https://wou.edu/chemistry/courses/online-chemistry-textbooks/ch450-and-ch451-biochemistry-defining-life-at-the-molecular-level/chapter-13-transcriptional-control-and-epigenetics/)  
52. Transcription factors and evolution: An integral part of gene expression (Review), accessed March 27, 2025, [https://www.spandidos-publications.com/10.3892/wasj.2020.32](https://www.spandidos-publications.com/10.3892/wasj.2020.32)  
53. Transcription factor-mediated epigenetic regulation of cell growth and phenotype for biological control and cancer \- PubMed Central, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC2862808/](https://pmc.ncbi.nlm.nih.gov/articles/PMC2862808/)  
54. Epigenetic Regulation of Noncoding Rna Transcription by Mammalian Rna Polymerase III, accessed March 27, 2025, [https://www.tandfonline.com/doi/full/10.2217/epi-2016-0108](https://www.tandfonline.com/doi/full/10.2217/epi-2016-0108)  
55. Design principles of the sparse coding network and the role of “sister cells” in the olfactory system of Drosophila \- Frontiers, accessed March 27, 2025, [https://www.frontiersin.org/journals/computational-neuroscience/articles/10.3389/fncom.2013.00141/full](https://www.frontiersin.org/journals/computational-neuroscience/articles/10.3389/fncom.2013.00141/full)  
56. Efficient sparse coding algorithms \- NIPS papers, accessed March 27, 2025, [http://papers.neurips.cc/paper/2979-efficient-sparse-coding-algorithms.pdf](http://papers.neurips.cc/paper/2979-efficient-sparse-coding-algorithms.pdf)  
57. Sparse Coding \- Unsupervised Feature Learning and Deep Learning Tutorial, accessed March 27, 2025, [http://ufldl.stanford.edu/tutorial/unsupervised/SparseCoding/](http://ufldl.stanford.edu/tutorial/unsupervised/SparseCoding/)  
58. Lecture 15 Sparse Coding \- Bernstein Netzwerk Computational Neuroscience, accessed March 27, 2025, [https://bernstein-network.de/wp-content/uploads/2021/03/Lecture-15-Sparse-coding-2020.pdf](https://bernstein-network.de/wp-content/uploads/2021/03/Lecture-15-Sparse-coding-2020.pdf)  
59. Sparse coding \- Scholarpedia, accessed March 27, 2025, [http://www.scholarpedia.org/article/Sparse\_coding](http://www.scholarpedia.org/article/Sparse_coding)  
60. Cell type specific information transfer for sparse coding \- bioRxiv, accessed March 27, 2025, [https://www.biorxiv.org/content/10.1101/2020.11.06.371658v4](https://www.biorxiv.org/content/10.1101/2020.11.06.371658v4)  
61. (PDF) scParser: sparse representation learning for scalable single-cell RNA sequencing data analysis \- ResearchGate, accessed March 27, 2025, [https://www.researchgate.net/publication/383180131\_scParser\_sparse\_representation\_learning\_for\_scalable\_single-cell\_RNA\_sequencing\_data\_analysis](https://www.researchgate.net/publication/383180131_scParser_sparse_representation_learning_for_scalable_single-cell_RNA_sequencing_data_analysis)  
62. Efficient feature extraction from highly sparse binary genotype data for cancer prognosis prediction using an auto-encoder \- PMC, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC9872139/](https://pmc.ncbi.nlm.nih.gov/articles/PMC9872139/)  
63. A Novel Algorithm for Feature Selection Using Penalized Regression with Applications to Single-Cell RNA Sequencing Data \- MDPI, accessed March 27, 2025, [https://www.mdpi.com/2079-7737/11/10/1495](https://www.mdpi.com/2079-7737/11/10/1495)  
64. Epigenetics Analysis and Integrated Analysis of Multiomics Data, Including Epigenetic Data, Using Artificial Intelligence in the Era of Precision Medicine \- PubMed Central, accessed March 27, 2025, [https://pmc.ncbi.nlm.nih.gov/articles/PMC7023005/](https://pmc.ncbi.nlm.nih.gov/articles/PMC7023005/)  
65. Compressed sensing \- Wikipedia, accessed March 27, 2025, [https://en.wikipedia.org/wiki/Compressed\_sensing](https://en.wikipedia.org/wiki/Compressed_sensing)  
66. Compressive Sensing: Methods, Techniques, and Applications \- ResearchGate, accessed March 27, 2025, [https://www.researchgate.net/publication/350581358\_Compressive\_Sensing\_Methods\_Techniques\_and\_Applications](https://www.researchgate.net/publication/350581358_Compressive_Sensing_Methods_Techniques_and_Applications)  
67. Compressive Sensing Introduction NOAA's satellite and radar systems collect and transmit over 1TB of data on a daily basis. Pr, accessed March 27, 2025, [https://sab.noaa.gov/wp-content/uploads/2021/08/compressive-sensing\_Joseph\_Final.pdf](https://sab.noaa.gov/wp-content/uploads/2021/08/compressive-sensing_Joseph_Final.pdf)  
68. Resources to understand compressed sensing? : r/compsci \- Reddit, accessed March 27, 2025, [https://www.reddit.com/r/compsci/comments/zjm6l2/resources\_to\_understand\_compressed\_sensing/](https://www.reddit.com/r/compsci/comments/zjm6l2/resources_to_understand_compressed_sensing/)  
69. Compressive Sensing: From Theory to Applications, a Survey \- RomiSatriaWahono.Net, accessed March 27, 2025, [https://romisatriawahono.net/lecture/rm/survey/network%20security/Qaisar%20-%20Compressive%20Sensing%20-%202013.pdf](https://romisatriawahono.net/lecture/rm/survey/network%20security/Qaisar%20-%20Compressive%20Sensing%20-%202013.pdf)  
70. unified hypothesis-free feature extraction framework for diverse epigenomic data | Bioinformatics Advances | Oxford Academic, accessed March 27, 2025, [https://academic.oup.com/bioinformaticsadvances/advance-article/doi/10.1093/bioadv/vbaf013/8066071?searchresult=1](https://academic.oup.com/bioinformaticsadvances/advance-article/doi/10.1093/bioadv/vbaf013/8066071?searchresult=1)  
71. Compressive forces induce epigenetic activation of aged human dermal fibroblasts through ERK signaling pathway | bioRxiv, accessed March 27, 2025, [https://www.biorxiv.org/content/10.1101/2024.11.04.621794v1.full-text](https://www.biorxiv.org/content/10.1101/2024.11.04.621794v1.full-text)  
72. Compressive forces induce epigenetic activation of aged human dermal fibroblasts through ERK signaling pathway \- bioRxiv, accessed March 27, 2025, [https://www.biorxiv.org/content/10.1101/2024.11.04.621794v1.full.pdf](https://www.biorxiv.org/content/10.1101/2024.11.04.621794v1.full.pdf)