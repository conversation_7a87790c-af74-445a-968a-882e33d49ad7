[{"Question": "什么是OpenAI Deep Research?", "prompt": "You are a reasoning assistant with the ability to perform web searches to help you answer the user's question accurately. You have special tools:\n\n- To perform a search: write <|begin_search_query|> your query here <|end_search_query|>.\nThen, the system will search and analyze relevant web pages, then provide you with helpful information in the format <|begin_search_result|> ...search results... <|end_search_result|>.\n\nYou can repeat the search process multiple times if necessary. The maximum number of search attempts is limited to 20.\n\nOnce you have all the information you need, continue your reasoning.\n\nExample:\nQuestion: \"<PERSON> is the voice of <PERSON> in a video game developed by which company?\"\nAssistant thinking steps:\n- I need to find out who voices <PERSON> in the video game.\n- Then, I need to determine which company developed that video game.\n\nAssistant:\n<|begin_search_query|>Alice <PERSON> voice<|end_search_query|>\n\n(System returns processed information from relevant web pages)\n\nAssistant thinks: The search results indicate that <PERSON> is the voice of <PERSON> in a specific video game. Now, I need to find out which company developed that game.\n\nAssistant:\n<|begin_search_query|>video game developed by <PERSON><|end_search_query|>\n\n(System returns processed information from relevant web pages)\n\nAssistant continues reasoning with the new information...\n\nRemember:\n- Use <|begin_search_query|> to request a web search and end with <|end_search_query|>.\n- When done searching, continue your reasoning.\n\nPlease answer the following question. You should think step by step to solve it.\n\nProvide your final answer in the format \\boxed{YOUR_ANSWER}.\n\nQuestion:\n什么是OpenAI Deep Research?\n\n", "Output": "为了回答“什么是OpenAI Deep Research”，我需要查找有关OpenAI Deep Research的具体信息，包括它的目的、活动以及与其他研究项目的关系。\n\n我将开始搜索相关信息。\n\n<|begin_search_query|>OpenAI Deep Research 介绍 目的 活动<|end_search_query|>\n\n<|begin_search_result|>OpenAI's Deep Research is an advanced AI capability designed to autonomously conduct complex multi-step research across the internet. It integrates various sources of information to produce comprehensive reports. The primary objectives of Deep Research include enhancing the accuracy and depth of information retrieval while addressing challenges like information fragmentation and inaccuracies often associated with traditional search methods.\n\nDeep Research functions by:\n1. **Autonomous Web Research**: It autonomously searches, reads, and synthesizes information from multiple sources, providing users with comprehensive insights.\n2. **Dynamic Reasoning**: The system adapts its search strategies based on new findings and can execute Python code for data processing and visualization, further enhancing its analytical capabilities.\n3. **Applications**: It is particularly useful in academic and scientific research, business intelligence, data journalism, and technical analysis, allowing users to aggregate and synthesize vast amounts of information efficiently.\n\nThe system leverages multi-task reinforcement learning and is designed to improve accuracy and reliability in research outputs, making it suitable for professional users who require in-depth analysis and reliable data for decision-making. \n\nOverall, Deep Research represents a significant advancement in AI-assisted research, aiming to streamline the research process and produce high-quality, reliable outputs.<|end_search_result|>\n\nOpenAI Deep Research 是一种先进的人工智能能力，旨在自主进行复杂的多步骤研究。它能够整合来自不同来源的信息，以生成全面的报告。Deep Research 的主要目标包括提高信息检索的准确性和深度，同时解决传统搜索方法中常见的信息碎片化和不准确性问题。\n\nDeep Research 的功能包括：\n1. **自主网络研究**：它能够自主搜索、阅读和综合来自多个来源的信息，为用户提供全面的见解。\n2. **动态推理**：该系统根据新的发现调整其搜索策略，并能够执行 Python 代码进行数据处理和可视化，从而增强其分析能力。\n3. **应用领域**：特别适用于学术和科学研究、商业情报、数据新闻和技术分析，使用户能够高效地聚合和综合大量信息。\n\n该系统利用多任务强化学习，旨在提高研究输出的准确性和可靠性，适合需要深入分析和可靠数据以做出决策的专业用户。\n\n总体而言，Deep Research 代表了人工智能辅助研究的重大进步，旨在简化研究过程并生成高质量、可靠的输出。\n\n因此，OpenAI Deep Research 是一个旨在提升研究效率和质量的系统，具有广泛的应用潜力。 \n\n最终答案为 \\boxed{OpenAI Deep Research 是一种先进的人工智能能力，旨在自主进行复杂的多步骤研究，提升信息检索的准确性和深度。}", "WebExplorer": [{"search_query": "OpenAI Deep Research 介绍 目的 活动", "Input": "You are a web explorer analyzing search results to find relevant information based on a given search query and search intent.\n\n**Guidelines:**\n\n1. **Analyze the Searched Web Pages:**\n- Carefully review the content of each searched web page.\n- Identify factual information that is relevant to the **Current Search Query** and can aid in the reasoning process for the original question.\n\n2. **More Information Seeking:**\n- If the information is not relevant to the query, you could:\n  1. Search again: <|begin_search_query|>another search query<|end_search_query|>\n  2. Access webpage content using: <|begin_click_link|>your URL<|end_click_link|>\n\n3. **Extract Relevant Information:**\n- Return the relevant information from the **Searched Web Pages** that is relevant to the **Current Search Query**.\n\n4. **Output Format:**\n- Present the information beginning with **Final Information** as shown below.\n\n**Final Information**\n[Relevant information]\n\n**Inputs:**\n\n- **Current Search Query:**\nOpenAI Deep Research 介绍 目的 活动\n\n- **Detailed Search Intent:**\nThe current search intent is to gather detailed information about \"OpenAI Deep Research.\" The user is specifically looking for an introduction to the program, its objectives, and the activities it engages in. Additionally, the user may be interested in understanding how OpenAI Deep Research relates to other research projects. This indicates a desire for comprehensive insights into the nature and scope of OpenAI Deep Research.\n\n- **Searched Web Pages:**\n***Web Page 1:***\n{\n  \"id\": 1,\n  \"title\": \"Introducing deep research - OpenAI\",\n  \"url\": \"https://openai.com/index/introducing-deep-research/\",\n  \"site_name\": \"\",\n  \"date\": \"\",\n  \"snippet\": \"Missing: 目的 活动\",\n  \"context\": \"\",\n  \"page_info\": \"Can not fetch the page content.\"\n}\n***Web Page 2:***\n{\n  \"id\": 2,\n  \"title\": \"Deep Research 是什麼？比較OpenAI、Google 深度研究的AI Agent\",\n  \"url\": \"https://ikala.ai/zh-tw/blog/ikala-ai-insight/deep-research-intro/\",\n  \"site_name\": \"\",\n  \"date\": \"28 Mar 2025\",\n  \"snippet\": \"Deep Research 是一種結合自動化檢索、跨來源分析與資料驗證的先進研究技術，旨在解決傳統搜尋與AI 搜尋常見的資訊碎片化與AI 幻覺的問題。相較於一般搜尋 ...\",\n  \"context\": \"\",\n  \"page_info\": \"Deep Research 是什麼？比較OpenAI、Google 深度研究的 AI Agent - iKala 跳至主要內容 iKala 選單 產品服務 Kolr iKala Nexus iKala Cloud 部落格 人才招募 最新消息 關於我們 聯絡我們 繁體中文 English 日本語 關閉選單 產品服務 顯示子選單 Kolr iKala Nexus iKala Cloud 部落格 人才招募 最新消息 關於我們 聯絡我們 繁體中文 顯示子選單 English 日本語 AI 趨勢洞察 Deep Research 是什麼？比較OpenAI、Google 深度研究的 AI Agent 2025-03-28 Home » AI 趨勢洞察 » Deep Research 是什麼？比較OpenAI、Google 深度研究的 AI Agent 在 AI 內容生成普及的時代，如何獲取準確、可靠的資訊成為關鍵挑戰，而這也正是 Deep Research 發揮作用的時刻——作為 AI Agent 的進階應用，Deep Research 能自動搜尋、跨來源比對與智慧分析，有效減少錯誤資訊與 AI 幻覺，幫助企業與專業人士獲得更可靠的研究結果，提升決策效率。 OpenAI、Google、Perplexity AI 等企業也都加速布局 Deep Research，強化 AI 搜尋與研究能力，使其成為 AI 技術競爭新焦點。本文將介紹 Deep Research 基礎概念，與 AI 搜尋的比較，以及 Deep Research 應用場景和常見工具整理。 Deep Research 是什麼？ Deep Research 是一種結合自動化檢索、跨來源分析與資料驗證的先進研究技術，旨在解決傳統搜尋與 AI 搜尋常見的資訊碎片化與 AI 幻覺的問題。相較於一般搜尋引擎或單次 AI 查詢，Deep Research 能夠多次自動檢索、整合多個可信資料來源，並生成帶有來源標註的完整報告，大幅提升資訊的正確性與研究深度。 以企業進行競爭對手分析為例，過去仰賴傳統搜尋引擎，使用者需點開大量連結、篩選資訊，還要避開廣告與錯誤內容，耗時又低效。現在，雖然 AI 搜尋能提升檢索速度，但卻容易因 AI 幻覺（Hallucination）出現錯誤、未經驗證的資訊，增加決策風險。 Deep Research 的誕生，正是為了解決這些痛點。下方將進一步整理 Deep Research 的操作流程與主要技術特色，幫助你快速掌握這項革新技術，讓資料分析與決策更快、更準確。 ✦延伸閱讀： AI搜尋時代來臨！企業 AI Search 應用案例與 5 大實踐步驟 Deep Research 怎麼用？ 目前，OpenAI、Google Gemini 跟 Perplexity 都有推出 Deep Research 應用，儘管每個平台操作不相同，但統整以下大致操作步驟 1. 進入平台 進入 Google Gemini、Perplexity 網站，並選擇「Deep Research 深度研究」選項。這一功能專為需要深入分析和研究的使用者而設計，能夠提供更全面的資料和見解。 2. 發起研究 使用者可以透過自然語言輸入研究主題或問題，就像與一般的 AI Chatbot 互動一樣。 例如，使用者可以針對特定市場趨勢、科學議題或歷史事件進行深入研究。 3. AI 制定研究計畫 Deep Research 會根據使用者的要求，制定一個研究計畫，計畫包括在網路上搜尋到的相關資訊、分析找到的資料、根據分析結果進一步的搜尋，並重複這些步驟，直到獲得全面的理解。 4. 使用者檢視與調整 在實際進行分析之前，AI 會列出預計分析的流程讓使用者確認，並且進一步作細節調整，之後即可由系統製作分析報告。 5. AI 產生研究報告 Deep Research 會將研究結果整理成一份報告，報告內容包括摘要重點、引用來源、圖表和視覺化資料，藉由圖表的方式也能讓更容易理解數據差異。 Deep Research 技術特色 Deep Research 技術的發展希望達到如同人類研究員處理專案的方式，從制定計劃到搜尋資料，並擁有 AI 的處理速度，以下是 Deep Research的技術特色 關鍵字與語意分析 Deep Research 透過關鍵字與語意分析來精確鎖定研究範疇，使用專業術語、同義詞匹配及自然語言處理 (NLP) 技術，確保檢索的相關性。這不僅能提高搜索效率，還能理解查詢背後的語境。 跨資料庫檢索 Deep Research 依賴跨資料庫檢索技術，確保從不同來源獲取權威數據，像是可能涵蓋學術文獻庫（如 Google Scholar、PubMed）、企業資料庫（如 Bloomberg、Gartner）以及法規與專利檢索系統。透過跨資料庫比對，研究者能夠整合來自不同領域的最新資訊。 上下文理解與知識圖譜應用 Deep Research 透過知識圖譜 (Knowledge Graph) 建立資訊之間的關聯，使研究不僅限於關鍵字匹配，而能形成邏輯性的知識網路，透過這種技術有助於理解概念間的因果關係，發掘隱藏的模式。 多步驟推理與論證 為確保研究的準確性，Deep Research 採用多步驟推理與論證方法，透過邏輯分析與數據驗證來建立可靠結論。它會交叉比對來自不同來源的資訊，找出趨勢的一致性，排除過時或偏頗的數據。 產出報告與結論 Deep Research 不僅限於資料蒐集，更強調高品質的研究輸出，透過整合資訊生成專業報告、競爭分析、趨勢預測等內容。這些報告通常包含詳細的數據分析、可視化圖表、案例研究與完整的參考文獻，確保透明度與可追溯性。 Deep Research 與 AI Search差異比較 從傳統手動搜尋到 AI 搜尋已經加快資料蒐整的速度了，為何還需要 Deep Research？AI Search 著重於即時檢索與簡單摘要，而 Deep Research 則進一步強調資訊的準確性、交叉比對與因果分析，確保結果不僅全面且可信，以下整理 AI 搜尋與深度研究的比較表格 比較項目 Deep Research AI Search 目的 深入探索與分析特定主題，適用於學術研究、企業決策及專業報告 提供快速、概括性的資訊回應，適用於一般知識查詢與即時問題解答 使用對象 研究人員、企業決策者、專家、學者 一般用戶、客服、內容創作者、行銷人員 資料搜尋方式 結構化與非結構化資料搜索 + 知識圖譜 + 多步驟推理 自然語言檢索 + 向量搜索 資料提供方式 提供詳細報告、數據分析、視覺化圖表，並附完整參考來源 即時生成的回答、摘要、推薦連結，提供簡短且易讀的資訊 資料來源 學術資料庫（如 IEEE、PubMed）、政府報告、企業研究、專業期刊等 網路公開資料、維基百科、新聞、論壇、社群媒體等 透明度 高透明度：通常提供完整的資料來源、引用文獻、資料出處 透明度較低，可能不提供完整來源或數據驗證，容易受資訊偏誤影響 案例 企業市場調查、醫學研究、法律分析、投資決策報告等 即時新聞搜尋、產品比較、客戶服務自動回覆、行銷內容生成等 同步提供實際案例比較，我們將以「阿茲海默症最新治療方法」進行深度研究與 AI 搜尋的比較， Deep Research 研究員透過深度研究工具搜尋「阿茲海默症的最新治療方法」，Deep Research 會搜整 Google Scholar、PubMed、FDA 臨床試驗資料庫的資訊，確保所有資訊來自權威機構，再交叉比對不同醫學期刊的研究結果，找出一致的趨勢與數據支持，最終產出完整的文獻回顧與資料報告。 AI Search 一般用戶透過 AI 搜尋工具 ChatGPT、Perplexity 搜尋「阿茲海默症最新治療方法是什麼？」，AI Search 會根據 LLM 訓練的資料，可能來自新聞、Reddit 討論或維基百科，進行資料整理並提供簡要摘要。 Deep Research 5 大應用場景 Deep Research 不僅適用於學術研究，在數據驅動的時代，企業需要準確、即時且可驗證的資訊，而傳統搜尋或 AI 搜尋往往無法滿足這些需求，Deep Research 透過多來源交叉比對、智能分析與報告生成，幫助企業無論在經理人決策、競爭分析、技術創新、法規監管等領域也能發揮關鍵作用，以下提供 5 大應用場景 企業決策與市場分析 Deep Research 透過 AI 分析與數據比對，幫助企業\"\n}\n***Web Page 3:***\n{\n  \"id\": 3,\n  \"title\": \"Deep Research 是什麼？實測介紹ChatGPT 深度研究新功能\",\n  \"url\": \"https://readingoutpost.com/deep-research-chatgpt/\",\n  \"site_name\": \"\",\n  \"date\": \"2 Mar 2025\",\n  \"snippet\": \"總結一下，OpenAI 的Deep Research 功能就像是一個會幫你「做研究的AI 代理人」，它能主動搜尋、整理、分析資料，幫你省下不少時間和精力。無論你是科技愛好 ...\",\n  \"context\": \"\",\n  \"page_info\": \"Deep Research æ˜¯ä»€éº¼ï¼Ÿå¯¦æ¸¬ä»‹ç´¹ ChatGPT æ·±åº¦ç ”ç©¶æ–°åŠŸèƒ½ | é–±è®€å‰�å“¨ç«™ Skip to content é–±è®€ç­†è¨˜ Expand ä¸»é¡Œåˆ†é¡� æ‰€æœ‰é–±è®€ç­†è¨˜ æ�¨è–¦æ›¸å–®ã€�å¥½ç”¨çš„é–±è®€å·¥å…· å€‹äººæˆ�é•· å¦‚ä½•å­¸ç¿’ã€�æ·±åº¦æ€�è€ƒã€�è‡ªæˆ‘æ�¢ç´¢ è�·å ´å·¥ä½œ è�·å ´å·¥ä½œè¡“ã€�ç®¡ç�†æŠ€å·§ã€�é ˜å°�åŠ› æŠ•è³‡ç�†è²¡ æŒ‡æ•¸æŠ•è³‡ã€�é‡‘è��è²¡å‹™ã€�é‡‘éŒ¢è§€å¿µ æ��é«˜ç”Ÿç”¢åŠ› æ™‚é–“ç®¡ç�†ã€�ç¿’æ…£é¤Šæˆ�ã€�ç”Ÿæ´»æ•ˆç�‡ ã€€ å•†æ¥­èˆ‡å‰µæ¥­ å•†æ¥­æ¨¡å¼�ã€�ä¼�æ¥­ç‡Ÿé�‹ã€�å‰µæ¥­æ€�è€ƒ å“�ç‰Œèˆ‡è¡ŒéŠ· å“�ç‰Œç¶“ç‡Ÿã€�è¡ŒéŠ·ç­–ç•¥èˆ‡æ–¹æ³• é–±è®€ã€�ç­†è¨˜èˆ‡å¯«ä½œ é–±è®€è¨£ç«…ã€�ç­†è¨˜æŠ€å·§ã€�å¯«ä½œæ–¹æ³• äººæ–‡èˆ‡ç§‘å­¸ æ–‡å­¸ã€�æ­·å�²ã€�å“²å­¸ã€�ç§‘å­¸ã€�å¿ƒç�† è‡ªå‚³èˆ‡å°�èªª å��äººå‚³è¨˜èˆ‡è‡ªå‚³ã€�å�„é¡�å�‹å°�èªª ç²¾é�¸æ–‡ç« å¦‚ä½•é–±è®€ä¸€æœ¬æ›¸ï¼Ÿ å­¸æœƒé–±è®€çš„å››å€‹å±¤æ¬¡ï¼Œæ��é«˜é–±è®€çš„æˆ�æ•ˆ å¦‚ä½• 30 åˆ†é�˜è®€å®Œä¸€æœ¬æ›¸ï¼Ÿ è®€å¾—å¿«ï¼Œå��è€Œæ›´å®¹æ˜“ç�†è§£æ›¸è£¡çš„å…§å®¹ å¦‚ä½•å¯«å¥½ä¸€ç¯‡è®€æ›¸å¿ƒå¾—ï¼Ÿ é¡›è¦†èˆŠè§€å¿µçš„ä¸‰ç¨®ç­†è¨˜å’Œå¯«ä½œæŠ€å·§ è®€æ›¸æ™‚æ‡‰è©²åœ¨å“ªè£¡ç•«ç·šï¼Ÿ å¾�ä½ å¦‚ä½•ç•«ç·šï¼Œå°±çŸ¥é�“æ˜¯å�¦è®€æ‡‚ä¸€æœ¬æ›¸ ğŸš€ å€‹äººæˆ�é•· ğŸ’¼ è�·å ´å·¥ä½œ ğŸ•˜ æ��é«˜ç”Ÿç”¢åŠ› ğŸ“� é–±è®€èˆ‡å¯«ä½œ ğŸ’¡ å•†æ¥­èˆ‡å‰µæ¥­ ğŸ’° æŠ•è³‡ç�†è²¡ ğŸ�¯ å“�ç‰Œèˆ‡è¡ŒéŠ· ğŸŒ� äººæ–‡èˆ‡ç§‘å­¸ ğŸ““ è‡ªå‚³èˆ‡å°�èªª å¥½æ›¸æ�¨è–¦ Expand Podcast èªªæ›¸ Podcast æ›¸ç±�é¡�æ�’è¡Œæ¦œ Top1 4000+è�¬æ¬¡ä¸‹è¼‰æ”¶è�½æ•¸ ä¸»é¡Œåˆ†é¡� æ‰€æœ‰å¥½æ›¸æ�¨è–¦ å¥½æ›¸æ�¨è–¦ï¼�æ›¸å–®å›�é¡§ ä¸»é¡Œç²¾é�¸å¥½æ›¸ å�„é ˜åŸŸä¸»é¡Œçš„å¿…è®€å¥½æ›¸æ�¨è–¦ å¹´åº¦ç²¾é�¸å¥½æ›¸ æ¯�å¹´ Top 10 çš„å¿…è®€å¥½æ›¸æ�¨è–¦ å°ˆå®¶ç²¾é�¸å¥½æ›¸ å�„é ˜åŸŸå°ˆå®¶çš„å¿…è®€å¥½æ›¸æ�¨è–¦ æ„›æ›¸äººçš„50å€‹ç§�è—� æˆ‘æœ€å¸¸é€ è¨ªçš„æ›¸è©•ç¶²ç«™å’Œéƒ¨è�½æ ¼ ç²¾é�¸æ–‡ç« å�šå®¢ä¾†æš¢éŠ·æ�’è¡Œ 20 æœ¬æ›¸ æš¢éŠ·æ¦œä¸Šäººäººéƒ½åœ¨è®€çš„é‚£äº›å¥½æ›¸ å€‹äººæˆ�é•·çš„å¿…è®€ 10 æœ¬æ›¸ å¹«ä½ å¯©è¦–å…§åœ¨ã€�æ‹“å¯¬æ€�ç¶­ã€�å¢�å¼·å¿ƒæ™º 2024 å¹´æˆ‘æœ€æ„›çš„ 10 æœ¬æ›¸ æ¯�å¹´æˆ‘éƒ½æœƒç²¾é�¸ Top 10 å¿…è®€å¥½æ›¸ æˆ‘æ�¨è–¦çš„ 12 æœ¬è‹±æ–‡æœ‰è�²æ›¸ è�½æœ‰è�²æ›¸æ��é«˜é–±è®€é‡�ã€�æ´»ç”¨é›¶ç¢�æ™‚é–“ ä¸‹ä¸€æœ¬è®€ä»€éº¼ Podcast èªªæ›¸ æ‰€æœ‰å¿…è®€å¥½æ›¸æ�¨è–¦ ä¸»é¡Œç²¾é�¸å¥½æ›¸ å¹´åº¦ç²¾é�¸å¥½æ›¸ å°ˆå®¶ç²¾é�¸å¥½æ›¸ å�šå®¢ä¾†æš¢éŠ·æ¦œ 20 æœ¬å¥½æ›¸ 2024 å¹´æˆ‘æœ€å–œæ­¡çš„ 10 æœ¬æ›¸ 2023 å¹´æˆ‘æœ€å–œæ­¡çš„ 10 æœ¬æ›¸ 2022 å¹´æˆ‘æœ€å–œæ­¡çš„ 10 æœ¬æ›¸ é–±è®€å¯¦è¸� Expand ç¿’æ…£å……é›»ç«™é �é�¢ ã€�å¥½æ–‡æ�¨è–¦ã€‘ç¿’æ…£è¿½è¹¤å¤±æ•—çš„å�Ÿå› æˆ‘å¿ƒä¸­ç¿’æ…£é¤Šæˆ�çš„æœ€å¥½æ–¹æ³• ä¸»é¡Œåˆ†é¡� æ‰€æœ‰é–±è®€å¯¦è¸� å¦‚ä½•å¯¦è¸�é–±è®€çš„æ‰€å­¸ï¼Œæ”¹è®Šäººç”Ÿ ç¿’æ…£é¤Šæˆ�æ³• é¤Šæˆ�å¥½ç¿’æ…£ã€�æˆ’é™¤å£�ç¿’æ…£çš„æ–¹æ³• å­�å½ˆç­†è¨˜æ³• è¿½è¹¤é��å�»ã€�é‡�æ¸…ç�¾åœ¨ã€�è¨­è¨ˆæœªä¾† å�¡ç‰‡ç›’ç­†è¨˜æ³• éš¨æ™‚é–“æˆ�é•·çš„çŸ¥è­˜ç®¡ç�†ç³»çµ± ç”Ÿæ´»èˆ‡å·¥ä½œè¨£ç«… å¾�å®¹æ‡‰å°�æŒ‘æˆ°ã€�å‰µé€ ç¾�å¥½æœªä¾† ç²¾é�¸æ–‡ç« é¤Šæˆ�ä¸€å¹´è®€ 50 æœ¬æ›¸çš„ç¿’æ…£ é¤Šæˆ�ä¸‰å€‹ç¿’æ…£ï¼Œé€�é��é–±è®€æ”¹è®Šäººç”Ÿ é€£çºŒå¯«ä¸€å¹´å­�å½ˆç­†è¨˜çš„æ”¶ç©« æˆ‘å­¸åˆ°å“ªä¸ƒä»¶äº‹ï¼Ÿä»¥å�Šå‹™å¯¦çš„å»ºè­° ä½¿ç”¨å…©å¹´å�¡ç‰‡ç›’ç­†è¨˜çš„æ”¶ç©« é€™å¥—çŸ¥è­˜ç®¡ç�†æ–¹æ³•å¸¶çµ¦æˆ‘çš„äº”å€‹æ”¹è®Š ã€Œç¶²ç«™æ”¹ç‰ˆã€�çš„ç›®çš„æ˜¯ä»€éº¼ï¼Ÿ æˆ‘å¾� 2025 å¹´ç¶²ç«™æ”¹ç‰ˆå­¸åˆ°çš„ä¸‰ä»¶äº‹ æ‰€æœ‰é–±è®€å¯¦è¸� ç¿’æ…£é¤Šæˆ�æ–¹æ³• å­�å½ˆç­†è¨˜æ³• å�¡ç‰‡ç›’ç­†è¨˜æ³• ç”Ÿæ´»èˆ‡å·¥ä½œè¨£ç«… ç¿’æ…£å……é›»ç«™ï½œç¿’æ…£é¤Šæˆ� App åŒ–è¼¸å…¥ç‚ºè¼¸å‡ºï½œç·šä¸Šèª²ç¨‹ å�¡ç‰‡ç›’ç­†è¨˜æ³•ï½œç·šä¸Šèª²ç¨‹ çŸ¥è­˜ç®¡ç�†å·¥å…· Expand å�¡ç‰‡ç›’ç­†è¨˜å¯¦æˆ°èª² ã€�ç²¾é�¸ã€‘å�¡ç‰‡ç›’ç­†è¨˜å’Œç¬¬äºŒå¤§è…¦ï¼Ÿ ç”¨åœ¨çŸ¥è­˜ç®¡ç�†å“ªå€‹æ¯”è¼ƒå¥½ï¼Ÿ è»Ÿé«”å·¥å…· Heptabase ç­†è¨˜è»Ÿé«”å¿ƒå¾— èº«ç‚ºç­†è¨˜æ�§çš„æˆ‘ï¼Œæ±ºå®šé�¸æ“‡ç”¨é€™æ¬¾ Audiopen AI èª�éŸ³è½‰æ–‡å­— ç”¨è¬›çš„å°±èƒ½å¯«æ–‡ï¼Œå¯¦ç�¾é›¶é˜»åŠ›å¯«ä½œ Pubué›»å­�æ›¸åŸ�ä½¿ç”¨å¿ƒå¾— æœ€å¤šå…ƒçš„æ•¸ä½�å…§å®¹ã€�çŸ¥è­˜å½±éŸ³å¹³å�° 1è™Ÿèª²å ‚ä½¿ç”¨å¿ƒå¾—è©•åƒ¹ çŸ¥è­˜å¤©å¤©æ»‘å…¥è€³è£¡ï¼Œæ—¥æ—¥è��æœƒå¿ƒè£¡ è³‡è¨Šåª’ä»‹ é›»å­�æ›¸é–±è®€å™¨ä½¿ç”¨å¿ƒå¾— è®“é–±è®€æ½›ç§»é»˜åŒ–ï¼Œè��å…¥ç”Ÿæ´»æ—¥å¸¸ä¸­ Audibleæœ‰è�²æ›¸ä½¿ç”¨å¿ƒå¾— å°‡ä¸Šç�­é€šå‹¤çš„å­¤å¯‚åŒ–æˆ�é–±è�½çš„äº«å®´ å“ˆä½›å•†æ¥­è©•è«–è¨‚é–±å¿ƒå¾— ä¸€æµ�ç®¡ç�†ã€�å¹«è�·æ¶¯é–‹åŠ é€Ÿç¥�å…µåˆ©å™¨ è�¯çˆ¾è¡—æ—¥å ±è¨‚é–±ä½¿ç”¨å¿ƒå¾— æ™‚é–“å¯¶è²´ï¼Œå�ªå�¸æ”¶å°‘é‡�å„ªè³ªçš„æ�±è¥¿ ã€Šå“ˆä½›å•†æ¥­è©•è«–ã€‹é›œèªŒè¨‚é–±å¿ƒå¾— ã€Šè�¯çˆ\"\n}\n***Web Page 4:***\n{\n  \"id\": 4,\n  \"title\": \"OpenAI也推可自動執行多步驟線上研究的Deep Research - iThome\",\n  \"url\": \"https://www.ithome.com.tw/news/167180\",\n  \"site_name\": \"\",\n  \"date\": \"3 Feb 2025\",\n  \"snippet\": \"OpenAI推出Deep Research功能，能夠整合網路上大量分散資訊，自動執行多步驟研究，最後產出報告結果。根據官方資料，該系統利用端對端的強化學習技術，經由 ...\",\n  \"context\": \"\",\n  \"page_info\": \"OpenAI也推可自動執行多步驟線上研究的Deep Research | iThome 移至主內容 新聞 專題 技術 AI Cloud 新聞 臺灣雲端大會 永續IT 醫療IT 資安 新聞 臺灣資安大會 研討會 研討會訊息 議程／講師徵求 社群 iT邦幫忙 IT EXPLAINED 搜尋 新聞 OpenAI也推可自動執行多步驟線上研究的Deep Research OpenAI推出Deep Research，運用強化學習與Python工具，自動執行多步驟研究，整合網路資訊產出報告，目前先開放給Pro用戶 文/ 李建興 | 2025-02-03 發表 OpenAI 推出 Deep Research功能，能夠整合網路上大量分散資訊，自動執行多步驟研究，最後產出報告結果。根據官方資料，該系統利用端對端的強化學習技術，經由大量針對困難瀏覽與推理任務的訓練，學會自主規畫並執行多步驟資訊搜尋流程。藉由與內建Python工具的整合，Deep Research不僅能夠繪製圖表、嵌入網站圖片，還能精確引用來源文字，以滿足研究報告對技術性與細節的需求。 從技術層面來看，Deep Research採用了強化學習與多模態推理相結合的方法，使得系統能夠根據使用者的提示自動調整搜尋策略。該模型可以在需要時主動回溯與調整搜尋路徑，進而應對網路上資訊量龐大且複雜的內容彙整挑戰。此外，其內建的Python工具能夠動態生成與迭代圖形，並將分析結果嵌入最終的報告中。Deep Research不僅可提供資料視覺化效果，也大幅降低了人力整理資訊的負擔。 根據OpenAI內部評估結果，在Humanity's Last Exam這項涵蓋多領域專家級問題的測試中，Deep Research取得了26.6％的準確率，相較於其他先進的語言模型，諸如OpenAI o3-mini_high 13.0％、DeepSeek-R1 9.4％與OpenAI o1 9.1％，Deep Research具有高精確的優勢。 另一項在GAIA基準測試平臺進行的試驗顯示，在面對3個不同難度等級的問題時，Deep Research在pass@1測試中，準確率分別達到74.29％、69.06％與47.6％，平均分67.36％，而在更密集的cons@64測試下，其各難度平均準確率更達到72.57％，這些資料呈現出Deep Research在多步驟推理、網頁瀏覽與工具運用上的綜合能力。 Deep Research目前主要提供給Pro用戶使用，未來將逐步擴展至Plus、Team與Enterprise等更多使用者。OpenAI表示，除了目前能夠存取的開放網路資料外，未來Deep Research還將連接更多專業訂閱資源與內部資料庫，以進一步提升報告資訊的豐富性與精準性。 在去年底時，Google就在其Gemini服務中 加入 了的Deep Research功能，採用Gemini 1.5 Pro模型，代使用者上網搜尋並深入研究複雜主題，生成條理清晰的報告，並支援直接匯出至Google Docs。史丹佛大學也推出類似Deep Research的研究計畫，稱之為 STORM ，採用Bing搜尋引擎尋找網路資料，助使用者編寫類似維基百科的文章。 熱門新聞 玉山銀行四階段導入Policy as Code，打造具彈性、共創的治理機制 2025-06-21 Cloudflare阻擋一波流量達7.3Tbps的史上最大DDoS攻擊 2025-06-23 研究人員揭露160億筆帳密資料外洩，疑透過竊資軟體收集、拼湊而成 2025-06-23 Minecraft遊戲玩家遭到鎖定，駭客假借提供修改工具竊取帳密資料 2025-06-23 Meta發表新款AI眼鏡Oakley Meta Glasses 2025-06-23 蘋果傳洽談收購Perplexity AI 2025-06-23 臺灣加密貨幣交易所幣託傳出5月遭遇攻擊事故，攻擊者身分是北韓駭客Lazarus 2025-06-23 M365 7月預設封鎖使用舊版身分驗證機制存取檔案 2025-06-20 Advertisement 專題報導 GenAI如何全面加速開發 臺灣首度引爆憑證信任危機 紅帽的GenAI逆襲 老牌數據大廠的代理AI新戰略 TVBS用AI走進媒體新時代 更多專題報導 電週文化事業版權所有、轉載必究  | Copyright © iThome 刊登廣告 訂閱週刊 授權服務 服務信箱 隱私權聲明與會員使用條款 資訊安全政策 關於iThome RSS 徵才\"\n}\n***Web Page 5:***\n{\n  \"id\": 5,\n  \"title\": \"Deep Research FAQ | OpenAI Help Center\",\n  \"url\": \"https://help.openai.com/en/articles/10500283-deep-research-faq\",\n  \"site_name\": \"\",\n  \"date\": \"\",\n  \"snippet\": \"Missing: 目的 活动\",\n  \"context\": \"\",\n  \"page_info\": \"Can not fetch the page content.\"\n}\n***Web Page 6:***\n{\n  \"id\": 6,\n  \"title\": \"OpenAI's Deep Research: A Guide With Practical Examples\",\n  \"url\": \"https://www.datacamp.com/blog/deep-research-openai\",\n  \"site_name\": \"\",\n  \"date\": \"\",\n  \"snippet\": \"Missing: 目的 活动\",\n  \"context\": \"\",\n  \"page_info\": \"Can not fetch the page content.\"\n}\n***Web Page 7:***\n{\n  \"id\": 7,\n  \"title\": \"Deep Research - Video - OpenAI Academy\",\n  \"url\": \"https://academy.openai.com/public/videos/deep-research-2025-03-11\",\n  \"site_name\": \"\",\n  \"date\": \"\",\n  \"snippet\": \"Missing: 目的 活动\",\n  \"context\": \"\",\n  \"page_info\": \"Deep Research - Video | OpenAI Academy OpenAI Academy Search +00:00 GMT SIGN IN Home Events Content Communities Help Home Events Content Communities Help Sign in or Join the community to continue Get Started Deep Research Posted Mar 01, 2025 | Views 28.9K # ChatGPT at Work Share Watch More 3:05 Use deep research with Github Posted May 15, 2025 | Views 6.6K # ChatGPT at Work 2:31 Market research with ChatGPT Posted Apr 13, 2025 | Views 37.1K # ChatGPT at Work # Foundational AI Literacy 13:31 AI for Academic Success: Research, Writing, and Studying Made Easier Posted Mar 22, 2025 | Views 36.5K # ChatGPT on Campus # Higher Education\"\n}\n***Web Page 8:***\n{\n  \"id\": 8,\n  \"title\": \"OpenAI Deep Research: The Future of Autonomous ... - Dirox\",\n  \"url\": \"https://dirox.com/post/openai-deep-research\",\n  \"site_name\": \"\",\n  \"date\": \"3 Feb 2025\",\n  \"snippet\": \"Deep Research by OpenAI: Not just a chatbot. This AI agent conducts complex online research, synthesizing data from multiple sources.\",\n  \"context\": \"\",\n  \"page_info\": \" The tool will expand to those regions once issues are resolved. ‍ Platform Availability Deep Research is currently on the web version of ChatGPT . Mobile and desktop apps are planned within the month for wider access and convenience. ‍ ‍ VIII. Limitations and Challenges ‍ Hallucination and Inferences Despite its advanced abilities, Deep Research can still hallucinate facts or make incorrect inferences . This means it might generate inaccurate information , even if it seems plausible. ‍ These errors may occur because the model is still learning context and nuances . Problems might arise in legal research, financial analysis, or medical studies , where even minor inaccuracies could be harmful. Users should critically evaluate and independently verify findings . ‍ Source Authority Deep Research struggles to distinguish authoritative information from rumors. It may treat less credible sources like reputable ones, or include misleading information. ‍ This makes fact-checking more important. Users should know that the model's findings are not guaranteed to be accurate, even with citations. They should corroborate the evidence , especially for sensitive decisions. ‍ Confidence Calibration Deep Research currently struggles with confidence calibration , meaning it often fails to convey uncertainty accurately. It may present information as factual even when the evidence is inconclusive or contradictory . ‍ For instance, if there is conflicting evidence, the model may not clearly indicate that there are multiple viewpoints or that the answer is ambiguous . This could result in a user mistakenly accepting erroneous facts as being definitive. ‍ Initial Imperfections There may be minor formatting errors in reports and citations. Also, tasks may sometimes take longer to start than anticipated. ‍ These initial issues are expected to improve as the model is used more frequently and as further updates are released. Check outputs carefully, especially during the early access phase . ‍ ‍ Conclusion ‍ Deep Research is a new AI designed for complex research , leveraging online sources . It aims to function like a fast research analyst , but users should always verify its findings due to potential inaccuracies or source issues. This tool is particularly useful for knowledge work or when making complex purchases . It utilizes the OpenAI o3 model and provides clear citations . ‍ Currently, Deep Research is available to ChatGPT Pro users , with expanded access planned soon. ‍ Deep Research showcases agentic AI , independently performing tasks. Moving beyond simple answers, it's likely to connect to more data sources and feature better visuals . Utilizing real-world tasks such as browsing and Python , it highlights AI’s potential for autonomous and unsupervised work. ‍ With advanced AI like Deep Research, we must recognize both its potential and responsibilities . Deep Research is poised to transform how we access and use knowledge. We must approach this new reality with discernment and critical thinking , ensuring AI enhances, rather than undermines, our understanding of the world. ‍ Ready to leverage cutting-edge technology for your business? Contact Dirox today for a free consultation and discover how AI can transform your operations. ‍ ‍ Other articles ByteDance BAGEL AI: The Free Alternative to GPT-4 Vision You Can Use Today Vietnam IT Outsourcing 2025: Market Reports & Trends The Future of Wealth Management: Trends, Challenges & Opportunities in 2025 Microsoft Bing Video Creator: Free AI Video Generation Guide Why Korean Startups Nearshore Their Software Development to Vietnam Inside Claude 4: A Critical Look at Anthropic's AI Advancement Digital Wallet Security: Why Robust Protection Is Non-Negotiable The Rise and Utility of AI Agents Ready to Launch Your Big Idea? Get a free consultation with software experts to turn your tech vision into reality. Name Email Address Message Thank you! Your submission has been received! Oops! Something went wrong while submitting the form. Started in 2003 in Ho Chi Minh City Vietnam, our Development Company oper\"\n}\n***Web Page 9:***\n{\n  \"id\": 9,\n  \"title\": \"Understanding complex trends with deep research | OpenAI\",\n  \"url\": \"https://openai.com/index/deep-research/\",\n  \"site_name\": \"\",\n  \"date\": \"\",\n  \"snippet\": \"Missing: 目的 活动\",\n  \"context\": \"\",\n  \"page_info\": \"Can not fetch the page content.\"\n}\n***Web Page 10:***\n{\n  \"id\": 10,\n  \"title\": \"OpenAI Released Deep Research System Card - Learn Prompting\",\n  \"url\": \"https://learnprompting.org/blog/openai-deep-research?srsltid=AfmBOorK5SaxyJ2uzV7vAF_ETEJCoEfQoAXvoF_PomIZLFyYt4OdtiaJ\",\n  \"site_name\": \"\",\n  \"date\": \"\",\n  \"snippet\": \"Missing: 目的 活动\",\n  \"context\": \"\",\n  \"page_info\": \"OpenAI Released Deep Research System Card Compete in HackAPrompt 2.0, the world's largest AI Red-Teaming competition! Check it out → Explore Courses AI Courses Master AI and prompt engineering Live Courses Interactive expert-led sessions Certification Exams Get Certified in AI DIFFICULTY LEVEL Beginner Courses Intermediate Courses Advanced Courses View All Courses RECOMMENDED COURSES NEW ChatGPT for Everyone Master ChatGPT fundamentals and advanced techniques Start Learning NEW Introduction to Prompt Engineering Learn the fundamentals of crafting effective prompts Start Learning ON-DEMAND AI Red-Teaming and AI Security Masterclass Learn AI security from the creator of Learn Prompting and HackAPrompt Start Learning LIVE Live Courses Interactive masterclasses with expert instructors Explore Live Courses Docs Blog Resources Community HackAPrompt 7-Day Free Course Prompt Hacking Guide Vocabulary Help Center Pricing For Business Live AI Workshops On Demand AI Courses Solutions About Start Learning for Free Log In OpenAI Released Deep Research System Card Valeriia Kuka March 3, 2025 3 minutes 🟢 easy Reading Level Deep Research is OpenAI's new agentic AI capability designed for conducting multi-step research across the internet. It can autonomously search, interpret, and analyze vast amounts of text, images, and PDFs to complete complex research tasks. The model is built on an early version of OpenAI o3 , optimized for web browsing and data analysis . Deep Research is designed to be adaptive , meaning it can pivot its search strategies as it encounters new information. It can also analyze user-provided files and run Python code for data processing and visualization . Key Features Deep Research goes beyond basic summarization and single-step querying . Instead of just retrieving information, it searches, evaluates, and synthesizes data, making it a much more capable research assistant. Autonomous web research : Searches, reads, and synthesizes information from multiple sources. Interprets diverse formats : Handles PDFs, images, and structured data. Python-based analysis : Executes calculations, creates visualizations, and processes datasets. Dynamic reasoning : Adjusts research direction based on new findings. Training and Technical Foundations Deep Research's capabilities are rooted in specialized training tailored for web-based research. The model was trained using new browsing datasets created specifically for research use cases. This process involved: Learning core browsing functions: Training included essential web tasks such as searching, clicking, scrolling, and file interpretation. Python tool integration: The model learned to operate a Python tool in a sandboxed setting, enabling it to perform calculations, data analysis, and graph plotting. Multi-task reinforcement learning: Through reinforcement learning, Deep Research was trained on a diverse range of tasks, from objective, auto-gradable tasks with ground truth answers to open-ended assignments guided by detailed rubrics. Safety-centric datasets: Alongside standard training data, the model was exposed to safety datasets from previous OpenAI o1 training, plus new browsing-specific safety datasets designed to tackle unique challenges in an internet research context. Applications and Use Cases Deep Research is poised to transform the landscape of complex research tasks. Its agentic capabilities make it highly versatile, suitable for: Academic and scientific research: Quickly aggregate and synthesize information from a multitude of sources to support in-depth studies. Business intelligence: Analyze market trends, competitive landscapes, and industry reports by parsing vast datasets online. Data journalism: Assist journalists in uncovering comprehensive narratives by connecting dots across diverse data points. Technical analysis: Read and interpret technical documents, code repositories, and white papers to provide detailed insights. The flexibility of Deep Research means that \"\n}\n\n\nNow please analyze the web pages and extract relevant information for the search query \"OpenAI Deep Research 介绍 目的 活动\" and the search intent.\n", "Output": "**Final Information**\nOpenAI's Deep Research is an advanced AI capability designed to autonomously conduct complex multi-step research across the internet. It integrates various sources of information to produce comprehensive reports. The primary objectives of Deep Research include enhancing the accuracy and depth of information retrieval while addressing challenges like information fragmentation and inaccuracies often associated with traditional search methods.\n\nDeep Research functions by:\n1. **Autonomous Web Research**: It autonomously searches, reads, and synthesizes information from multiple sources, providing users with comprehensive insights.\n2. **Dynamic Reasoning**: The system adapts its search strategies based on new findings and can execute Python code for data processing and visualization, further enhancing its analytical capabilities.\n3. **Applications**: It is particularly useful in academic and scientific research, business intelligence, data journalism, and technical analysis, allowing users to aggregate and synthesize vast amounts of information efficiently.\n\nThe system leverages multi-task reinforcement learning and is designed to improve accuracy and reliability in research outputs, making it suitable for professional users who require in-depth analysis and reliable data for decision-making. \n\nOverall, Deep Research represents a significant advancement in AI-assisted research, aiming to streamline the research process and produce high-quality, reliable outputs.", "Extracted_info": "OpenAI's Deep Research is an advanced AI capability designed to autonomously conduct complex multi-step research across the internet. It integrates various sources of information to produce comprehensive reports. The primary objectives of Deep Research include enhancing the accuracy and depth of information retrieval while addressing challenges like information fragmentation and inaccuracies often associated with traditional search methods.\n\nDeep Research functions by:\n1. **Autonomous Web Research**: It autonomously searches, reads, and synthesizes information from multiple sources, providing users with comprehensive insights.\n2. **Dynamic Reasoning**: The system adapts its search strategies based on new findings and can execute Python code for data processing and visualization, further enhancing its analytical capabilities.\n3. **Applications**: It is particularly useful in academic and scientific research, business intelligence, data journalism, and technical analysis, allowing users to aggregate and synthesize vast amounts of information efficiently.\n\nThe system leverages multi-task reinforcement learning and is designed to improve accuracy and reliability in research outputs, making it suitable for professional users who require in-depth analysis and reliable data for decision-making. \n\nOverall, Deep Research represents a significant advancement in AI-assisted research, aiming to streamline the research process and produce high-quality, reliable outputs."}]}]